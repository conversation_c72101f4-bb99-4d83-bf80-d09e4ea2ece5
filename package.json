{"name": "portal-ciudadano-chia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:setup": "npm run db:migrate && npm run db:seed", "db:migrate": "supabase db reset", "db:seed": "tsx scripts/seed-database.ts", "db:seed:tramites": "tsx scripts/seed-tramites.ts", "db:seed:opas": "tsx scripts/seed-opas.ts", "db:seed:faqs": "tsx scripts/seed-faqs.ts", "db:status": "tsx scripts/db-status.ts", "db:reset": "supabase db reset && npm run db:seed", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^15.4.1", "react": "^19.0.0", "react-dom": "^19.0.0", "@supabase/supabase-js": "^2.39.0", "@supabase/ssr": "^0.1.0"}, "devDependencies": {"@types/node": "^20.11.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "typescript": "^5.3.0", "eslint": "^8.56.0", "eslint-config-next": "^15.4.1", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@types/jest": "^29.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.0", "@testing-library/jest-dom": "^6.2.0", "tsx": "^4.7.0", "supabase": "^1.142.0"}}