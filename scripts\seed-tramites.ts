#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import { Database } from '../src/types/database'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

interface TramiteJSON {
  codigo_unico: string
  nombre: string
  formulario: string
  tiempo_respuesta: string
  tiene_pago: string
  visualizacion_suit?: string
  visualizacion_gov?: string
}

interface SubdependenciaJSON {
  nombre: string
  sigla: string
  tramites: TramiteJSON[]
}

interface DependenciaJSON {
  nombre: string
  sigla: string
  subdependencias: Record<string, SubdependenciaJSON>
  correo?: string
  direccion?: string
  extension?: string
  responsable?: string
}

interface TramitesData {
  dependencias: Record<string, DependenciaJSON>
}

async function seedTramites() {
  console.log('📄 Importando solo trámites...')
  
  // Limpiar datos existentes
  await supabase.from('tramites').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('subdependencias').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('dependencias').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  
  const tramitesPath = join(process.cwd(), 'docs', 'tramites_chia_optimo.json')
  const tramitesData: TramitesData = JSON.parse(readFileSync(tramitesPath, 'utf-8'))
  
  let dependenciasCount = 0
  let subdependenciasCount = 0
  let tramitesCount = 0
  
  for (const [codigoDep, dependencia] of Object.entries(tramitesData.dependencias)) {
    // Insertar dependencia
    const { data: depData, error: depError } = await supabase
      .from('dependencias')
      .insert({
        codigo: codigoDep,
        nombre: dependencia.nombre,
        sigla: dependencia.sigla,
        descripcion: `Correo: ${dependencia.correo || 'N/A'}, Dirección: ${dependencia.direccion || 'N/A'}, Responsable: ${dependencia.responsable || 'N/A'}`
      })
      .select()
      .single()
    
    if (depError) {
      console.error(`❌ Error insertando dependencia ${codigoDep}:`, depError)
      continue
    }
    
    dependenciasCount++
    console.log(`✅ Dependencia insertada: ${dependencia.nombre}`)
    
    // Insertar subdependencias y trámites
    for (const [codigoSub, subdependencia] of Object.entries(dependencia.subdependencias)) {
      const { data: subData, error: subError } = await supabase
        .from('subdependencias')
        .insert({
          codigo: codigoSub,
          nombre: subdependencia.nombre,
          sigla: subdependencia.sigla,
          dependencia_id: depData.id
        })
        .select()
        .single()
      
      if (subError) {
        console.error(`❌ Error insertando subdependencia ${codigoSub}:`, subError)
        continue
      }
      
      subdependenciasCount++
      
      // Insertar trámites
      for (const tramite of subdependencia.tramites) {
        const { error: tramiteError } = await supabase
          .from('tramites')
          .insert({
            codigo_unico: tramite.codigo_unico,
            nombre: tramite.nombre,
            formulario: tramite.formulario,
            tiempo_respuesta: tramite.tiempo_respuesta,
            tiene_pago: tramite.tiene_pago !== 'No' && tramite.tiene_pago !== '',
            visualizacion_suit: tramite.visualizacion_suit,
            visualizacion_gov: tramite.visualizacion_gov,
            subdependencia_id: subData.id
          })
        
        if (tramiteError) {
          console.error(`❌ Error insertando trámite ${tramite.codigo_unico}:`, tramiteError)
          continue
        }
        
        tramitesCount++
      }
    }
  }
  
  console.log(`🎉 Trámites importados exitosamente:`)
  console.log(`   - ${dependenciasCount} dependencias`)
  console.log(`   - ${subdependenciasCount} subdependencias`)
  console.log(`   - ${tramitesCount} trámites`)
}

async function main() {
  try {
    await seedTramites()
  } catch (error) {
    console.error('❌ Error durante la importación:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
