"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./src/services/dependenciasApi.ts":
/*!*****************************************!*\
  !*** ./src/services/dependenciasApi.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDependenciaByCodigo: () => (/* binding */ getDependenciaByCodigo),\n/* harmony export */   getDependenciaById: () => (/* binding */ getDependenciaById),\n/* harmony export */   getDependenciaStats: () => (/* binding */ getDependenciaStats),\n/* harmony export */   getDependencias: () => (/* binding */ getDependencias),\n/* harmony export */   getDependenciasResumen: () => (/* binding */ getDependenciasResumen),\n/* harmony export */   getSubdependenciasByDependencia: () => (/* binding */ getSubdependenciasByDependencia),\n/* harmony export */   getTramitesPopularesByDependencia: () => (/* binding */ getTramitesPopularesByDependencia),\n/* harmony export */   searchDependencias: () => (/* binding */ searchDependencias)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n\nconst supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n/**\n * Obtiene todas las dependencias activas\n */ async function getDependencias() {\n    const { data, error } = await supabase.from('dependencias').select('*').eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error fetching dependencias:', error);\n        throw new Error('Error al cargar las dependencias');\n    }\n    return data || [];\n}\n/**\n * Obtiene una dependencia específica por ID con sus subdependencias\n */ async function getDependenciaById(id) {\n    const { data, error } = await supabase.from('dependencias').select(\"\\n      *,\\n      subdependencias (\\n        *,\\n        tramites (count),\\n        opas (count)\\n      )\\n    \").eq('id', id).eq('activo', true).single();\n    if (error) {\n        console.error('Error fetching dependencia:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene una dependencia específica por código con sus subdependencias\n */ async function getDependenciaByCodigo(codigo) {\n    const { data, error } = await supabase.from('dependencias').select(\"\\n      *,\\n      subdependencias (\\n        *,\\n        tramites (count),\\n        opas (count)\\n      )\\n    \").eq('codigo', codigo).eq('activo', true).single();\n    if (error) {\n        console.error('Error fetching dependencia by codigo:', error);\n        return null;\n    }\n    return data;\n}\n/**\n * Obtiene todas las subdependencias de una dependencia específica\n */ async function getSubdependenciasByDependencia(dependenciaId) {\n    const { data, error } = await supabase.from('subdependencias').select(\"\\n      *,\\n      dependencia:dependencias (*),\\n      tramites (*),\\n      opas (*)\\n    \").eq('dependencia_id', dependenciaId).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error fetching subdependencias:', error);\n        throw new Error('Error al cargar las subdependencias');\n    }\n    return data;\n}\n/**\n * Obtiene estadísticas de una dependencia (número de trámites, OPAs, etc.)\n */ async function getDependenciaStats(dependenciaId) {\n    // Obtener conteo de trámites\n    const { count: tramitesCount } = await supabase.from('tramites').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('subdependencias.dependencia_id', dependenciaId).eq('activo', true);\n    // Obtener conteo de OPAs\n    const { count: opasCount } = await supabase.from('opas').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('subdependencias.dependencia_id', dependenciaId).eq('activo', true);\n    // Obtener conteo de FAQs\n    const { count: faqsCount } = await supabase.from('faqs').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('dependencia_id', dependenciaId).eq('activo', true);\n    // Obtener conteo de subdependencias\n    const { count: subdependenciasCount } = await supabase.from('subdependencias').select('*', {\n        count: 'exact',\n        head: true\n    }).eq('dependencia_id', dependenciaId).eq('activo', true);\n    return {\n        tramites: tramitesCount || 0,\n        opas: opasCount || 0,\n        faqs: faqsCount || 0,\n        subdependencias: subdependenciasCount || 0\n    };\n}\n/**\n * Obtiene los trámites más populares de una dependencia\n */ async function getTramitesPopularesByDependencia(dependenciaId) {\n    let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n    const { data, error } = await supabase.from('tramites').select(\"\\n      *,\\n      subdependencia:subdependencias (\\n        *,\\n        dependencia:dependencias (*)\\n      )\\n    \").eq('subdependencias.dependencia_id', dependenciaId).eq('activo', true).order('created_at', {\n        ascending: false\n    }).limit(limit);\n    if (error) {\n        console.error('Error fetching tramites populares:', error);\n        return [];\n    }\n    return data || [];\n}\n/**\n * Busca dependencias por nombre o descripción\n */ async function searchDependencias(query) {\n    const { data, error } = await supabase.from('dependencias').select('*').or(\"nombre.ilike.%\".concat(query, \"%,descripcion.ilike.%\").concat(query, \"%\")).eq('activo', true).order('nombre');\n    if (error) {\n        console.error('Error searching dependencias:', error);\n        throw new Error('Error en la búsqueda de dependencias');\n    }\n    return data || [];\n}\n/**\n * Obtiene el resumen de todas las dependencias con estadísticas básicas\n */ async function getDependenciasResumen() {\n    // Primero obtenemos las dependencias básicas\n    const { data: dependencias, error: depError } = await supabase.from('dependencias').select('*').eq('activo', true).order('nombre');\n    if (depError) {\n        console.error('Error fetching dependencias:', depError);\n        throw new Error('Error al cargar las dependencias');\n    }\n    if (!dependencias || dependencias.length === 0) {\n        return [];\n    }\n    // Luego obtenemos las estadísticas para cada dependencia\n    const dependenciasConStats = await Promise.all(dependencias.map(async (dep)=>{\n        // Contar subdependencias\n        const { count: subdependenciasCount } = await supabase.from('subdependencias').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('dependencia_id', dep.id).eq('activo', true);\n        // Contar FAQs\n        const { count: faqsCount } = await supabase.from('faqs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('dependencia_id', dep.id).eq('activo', true);\n        return {\n            ...dep,\n            subdependencias: [\n                {\n                    count: subdependenciasCount || 0\n                }\n            ],\n            faqs: [\n                {\n                    count: faqsCount || 0\n                }\n            ]\n        };\n    }));\n    return dependenciasConStats;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/dependenciasApi.ts\n"));

/***/ })

});