{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "vhwYRDmKTklOrt2WJmfpzD3uwm7x2ziESX0r6Gd8srI=", "__NEXT_PREVIEW_MODE_ID": "f5d49d063460c5dcb58b3a86fc8c9bfc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "75afe48cdfb364293a74c48fc74be861b199a788ed81413bbdea85eabe13f5d0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "889b2a74da41e7db20905d6729a37b63d54364b4b6a1f2f70be30a76272a43ee"}}}, "functions": {}, "sortedMiddleware": ["/"]}