@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Colores institucionales */
  --color-primary-yellow: #FFDC00;
  --color-primary-yellow-alt: #F8E000;
  --color-primary-green: #009045;
  --color-primary-green-alt: #009540;
  
  /* Variables de sistema */
  --background: #ffffff;
  --foreground: #171717;
  
  /* Espaciado */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 40px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Inter, Arial, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Estilos base para accesibilidad */
*:focus {
  outline: 2px solid var(--color-primary-yellow);
  outline-offset: 2px;
}

/* Estilos para componentes institucionales */
.btn-primary {
  @apply bg-primary-yellow text-black px-4 py-2 rounded font-medium hover:bg-primary-yellow-alt transition-colors focus:ring-2 focus:ring-primary-yellow focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-primary-green text-white px-4 py-2 rounded font-medium hover:bg-primary-green-alt transition-colors focus:ring-2 focus:ring-primary-green focus:ring-offset-2;
}

.card {
  @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow;
}

.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}
