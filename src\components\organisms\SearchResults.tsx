'use client'

import { useState } from 'react'
import Link from 'next/link'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import Loading from '@/components/atoms/Loading'

interface SearchResult {
  id: string
  tipo: 'tramite' | 'opa' | 'faq'
  titulo: string
  descripcion: string
  dependencia: string
  subdependencia?: string
  url: string
  rank: number
  // Campos adicionales según el tipo
  tiempo_respuesta?: string
  tiene_pago?: boolean
  tema?: string
  palabras_clave?: string[]
}

interface SearchResultsProps {
  results: SearchResult[]
  loading: boolean
  error: string | null
  query: string
  total: number
  onFilterChange?: (filters: SearchFilters) => void
}

interface SearchFilters {
  tipo?: 'tramite' | 'opa' | 'faq' | ''
  dependencia?: string
  tiene_pago?: boolean | null
}

export default function SearchResults({
  results,
  loading,
  error,
  query,
  total,
  onFilterChange
}: SearchResultsProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    tipo: '',
    dependencia: '',
    tiene_pago: null
  })

  const handleFilterChange = (newFilters: Partial<SearchFilters>) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)
    onFilterChange?.(updatedFilters)
  }

  // Obtener dependencias únicas para el filtro
  const uniqueDependencias = Array.from(
    new Set(results.map(r => r.dependencia))
  ).sort()

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loading size="lg" text="Buscando..." />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold">Error en la búsqueda</h3>
          <p className="text-gray-600 mt-2">{error}</p>
        </div>
      </div>
    )
  }

  if (!query.trim()) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-semibold">Realiza una búsqueda</h3>
          <p className="text-gray-600 mt-2">
            Escribe en el campo de búsqueda para encontrar trámites, OPAs o información
          </p>
        </div>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-semibold">No se encontraron resultados</h3>
          <p className="text-gray-600 mt-2">
            No hay resultados para "{query}". Intenta con otros términos de búsqueda.
          </p>
        </div>
        <div className="mt-6 text-sm text-gray-500">
          <p className="mb-2">Sugerencias:</p>
          <ul className="space-y-1">
            <li>• Verifica la ortografía de las palabras</li>
            <li>• Usa términos más generales</li>
            <li>• Prueba con sinónimos o palabras relacionadas</li>
          </ul>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header con estadísticas y filtros */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-primary-green">
            Resultados de búsqueda
          </h2>
          <p className="text-gray-600">
            {total} resultado{total !== 1 ? 's' : ''} para "{query}"
          </p>
        </div>

        {/* Filtros */}
        <div className="flex flex-wrap gap-3">
          {/* Filtro por tipo */}
          <select
            value={filters.tipo}
            onChange={(e) => handleFilterChange({ tipo: e.target.value as any })}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-primary-yellow focus:border-transparent"
          >
            <option value="">Todos los tipos</option>
            <option value="tramite">Trámites</option>
            <option value="opa">OPAs</option>
            <option value="faq">FAQs</option>
          </select>

          {/* Filtro por dependencia */}
          {uniqueDependencias.length > 1 && (
            <select
              value={filters.dependencia}
              onChange={(e) => handleFilterChange({ dependencia: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-primary-yellow focus:border-transparent"
            >
              <option value="">Todas las dependencias</option>
              {uniqueDependencias.map(dep => (
                <option key={dep} value={dep}>{dep}</option>
              ))}
            </select>
          )}

          {/* Filtro por pago (solo para trámites) */}
          {results.some(r => r.tipo === 'tramite') && (
            <select
              value={filters.tiene_pago === null ? '' : filters.tiene_pago.toString()}
              onChange={(e) => handleFilterChange({ 
                tiene_pago: e.target.value === '' ? null : e.target.value === 'true' 
              })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-primary-yellow focus:border-transparent"
            >
              <option value="">Con o sin pago</option>
              <option value="false">Gratuitos</option>
              <option value="true">Con pago</option>
            </select>
          )}
        </div>
      </div>

      {/* Lista de resultados */}
      <div className="space-y-4">
        {results.map((result) => (
          <SearchResultCard key={result.id} result={result} />
        ))}
      </div>
    </div>
  )
}

interface SearchResultCardProps {
  result: SearchResult
}

function SearchResultCard({ result }: SearchResultCardProps) {
  const getTipoColor = (tipo: string) => {
    switch (tipo) {
      case 'tramite': return 'bg-blue-100 text-blue-800'
      case 'opa': return 'bg-green-100 text-green-800'
      case 'faq': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTipoLabel = (tipo: string) => {
    switch (tipo) {
      case 'tramite': return 'Trámite'
      case 'opa': return 'OPA'
      case 'faq': return 'FAQ'
      default: return tipo
    }
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
        <div className="flex-1">
          {/* Header */}
          <div className="flex items-start gap-3 mb-3">
            <div className="flex-1">
              <Link 
                href={result.url}
                className="text-lg font-semibold text-primary-green hover:text-primary-green-alt transition-colors"
              >
                {result.titulo}
              </Link>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="info" size="sm" className={getTipoColor(result.tipo)}>
                  {getTipoLabel(result.tipo)}
                </Badge>
                {result.tiene_pago === false && result.tipo === 'tramite' && (
                  <Badge variant="success" size="sm">
                    Gratuito
                  </Badge>
                )}
                {result.tiene_pago === true && result.tipo === 'tramite' && (
                  <Badge variant="warning" size="sm">
                    Con pago
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Descripción */}
          {result.descripcion && (
            <p className="text-gray-600 mb-3 line-clamp-2">
              {result.descripcion}
            </p>
          )}

          {/* Información adicional */}
          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            <span>📍 {result.dependencia}</span>
            {result.subdependencia && (
              <span>🏢 {result.subdependencia}</span>
            )}
            {result.tiempo_respuesta && (
              <span>⏱️ {result.tiempo_respuesta}</span>
            )}
            {result.tema && (
              <span>🏷️ {result.tema}</span>
            )}
          </div>
        </div>

        {/* Acción */}
        <div className="lg:ml-4">
          <Link
            href={result.url}
            className="btn-primary inline-block text-center min-w-[120px]"
          >
            Ver detalles
          </Link>
        </div>
      </div>
    </Card>
  )
}
