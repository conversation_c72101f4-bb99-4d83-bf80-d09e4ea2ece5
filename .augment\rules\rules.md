---
type: "always_apply"
---

## Rules and User Guidelines for the IDE (Next.js + Supabase Project)

### CRITICAL: Always Check Project Status First
Before any development, ALWAYS run:
```bash
grep -A 5 "EN PROGRESO" docs/BACKLOG.md
grep -A 3 "\[ \]" docs/TASK.md
```

### Development Priority Order
1. **FIRST**: Complete stories marked "EN PROGRESO" in BACKLOG.md
2. **SECOND**: Follow Must Have → Should Have → Could Have
3. **THIRD**: Respect story dependencies (e.g., Story 1.1 requires Story 0.2)

### Current Critical Blocker
🔄 **Story 0.2: Data migration and initial population** (IN PROGRESS)
- Blocks: Stories 1.1, 1.2, 1.3, 2.1, 3.1
- Must complete before any other functionality

### General Principles
- Prioriza el diseño responsive/mobile-first en todos los componentes y vistas.
- Cumple con estándares de accesibilidad (WCAG AA), contraste alto, navegación por teclado y uso de aria-labels.
- Mantén la identidad visual municipal usando variables CSS para colores institucionales y tipografía definida.
- Usa TypeScript para tipar props, hooks y servicios.
- Separa lógica, presentación y estilos (componentes, hooks, CSS Modules).
- Documenta el código y los componentes con comentarios claros y JSDoc donde aplique.
- Realiza pruebas automáticas (unitarias y de integración) y revisa manualmente en dispositivos móviles reales.
- Protege la seguridad y privacidad de datos ciudadanos en todo momento.

### Guidelines for Collaborative Work
- Usa ramas feature/{nombre} para desarrollo de nuevas funcionalidades.
- Realiza pull requests descriptivos y solicita revisión de al menos un compañero.
- Sincroniza frecuentemente con main y resuelve conflictos antes de mergear.
- Mantén la documentación (README, specs, wireframes) actualizada en cada entrega relevante.
- Registra decisiones de arquitectura y convenciones en los archivos correspondientes (`ui-architecture.md`, `front-end-spec.md`).

### Coding Standards
- Nombra archivos y carpetas en inglés, en kebab-case o camelCase según convención del stack.
- Componentes React en PascalCase, hooks en camelCase.
- No dejes código muerto ni comentarios obsoletos.
- Prefiere funciones puras y hooks personalizados para lógica reutilizable.
- Usa variables de entorno para claves sensibles y endpoints.
- Valida y sanitiza toda entrada de usuario, especialmente en formularios y endpoints backend.

### UX/UI & Testing
- Todos los componentes deben ser accesibles y responsivos.
- Usa Storybook para documentar y probar componentes UI (si está disponible).
- Prueba flujos críticos con React Testing Library y Cypress, incluyendo mobile.
- El widget AI debe ser visible, accesible y no invasivo.

### Seguridad y Buenas Prácticas Backend
- Usa Row Level Security (RLS) en Supabase y roles diferenciados.
- Implementa auditoría de cambios y logs de acciones administrativas.
- Asegura HTTPS en todos los entornos.

### Comunicación y Feedback
- Reporta bugs y sugerencias en el issue tracker del repositorio.
- Participa en revisiones de código y sesiones de feedback UX.
- Propón mejoras de accesibilidad, performance o arquitectura cuando las detectes.

### Task Completion Protocol
1. Mark tasks as completed in `TASK.md`
2. Update story status in `BACKLOG.md`
3. Run tests and verify coverage
4. Update documentation if necessary
