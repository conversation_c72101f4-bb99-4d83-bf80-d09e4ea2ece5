'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import SearchBar from '@/components/molecules/SearchBar'
import SearchResults from '@/components/organisms/SearchResults'

interface SearchResult {
  id: string
  tipo: 'tramite' | 'opa' | 'faq'
  titulo: string
  descripcion: string
  dependencia: string
  subdependencia?: string
  url: string
  rank: number
  tiempo_respuesta?: string
  tiene_pago?: boolean
  tema?: string
  palabras_clave?: string[]
}

interface SearchFilters {
  tipo?: 'tramite' | 'opa' | 'faq' | ''
  dependencia?: string
  tiene_pago?: boolean | null
}

interface SearchPageContentProps {
  initialQuery: string
  initialTipo: string
  initialDependencia: string
}

export default function SearchPageContent({
  initialQuery,
  initialTipo,
  initialDependencia
}: SearchPageContentProps) {
  const [query, setQuery] = useState(initialQuery)
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [filters, setFilters] = useState<SearchFilters>({
    tipo: initialTipo as any || '',
    dependencia: initialDependencia || '',
    tiene_pago: null
  })

  const router = useRouter()

  // Función para realizar búsqueda
  const performSearch = useCallback(async (searchQuery: string, searchFilters: SearchFilters = {}) => {
    if (!searchQuery.trim()) {
      setResults([])
      setTotal(0)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        q: searchQuery.trim(),
        limit: '20'
      })

      if (searchFilters.tipo) {
        params.append('tipo', searchFilters.tipo)
      }
      if (searchFilters.dependencia) {
        params.append('dependencia', searchFilters.dependencia)
      }

      const response = await fetch(`/api/search/tramites?${params}`)
      
      if (!response.ok) {
        throw new Error('Error en la búsqueda')
      }

      const data = await response.json()
      
      // Aplicar filtros adicionales en el cliente
      let filteredResults = data.results || []
      
      if (searchFilters.tiene_pago !== null && searchFilters.tiene_pago !== undefined) {
        filteredResults = filteredResults.filter((result: SearchResult) => 
          result.tipo !== 'tramite' || result.tiene_pago === searchFilters.tiene_pago
        )
      }

      setResults(filteredResults)
      setTotal(filteredResults.length)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error en la búsqueda')
      setResults([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }, [])

  // Efecto para búsqueda inicial
  useEffect(() => {
    if (initialQuery) {
      performSearch(initialQuery, filters)
    }
  }, [initialQuery, performSearch, filters])

  // Manejar nueva búsqueda
  const handleSearch = (newQuery: string) => {
    setQuery(newQuery)
    
    // Actualizar URL
    const params = new URLSearchParams()
    if (newQuery.trim()) {
      params.set('q', newQuery.trim())
    }
    if (filters.tipo) {
      params.set('tipo', filters.tipo)
    }
    if (filters.dependencia) {
      params.set('dependencia', filters.dependencia)
    }

    const newUrl = `/buscar${params.toString() ? `?${params.toString()}` : ''}`
    router.push(newUrl)

    // Realizar búsqueda
    performSearch(newQuery, filters)
  }

  // Manejar cambio de filtros
  const handleFilterChange = (newFilters: SearchFilters) => {
    const updatedFilters = { ...filters, ...newFilters }
    setFilters(updatedFilters)

    // Actualizar URL
    const params = new URLSearchParams()
    if (query.trim()) {
      params.set('q', query.trim())
    }
    if (updatedFilters.tipo) {
      params.set('tipo', updatedFilters.tipo)
    }
    if (updatedFilters.dependencia) {
      params.set('dependencia', updatedFilters.dependencia)
    }

    const newUrl = `/buscar${params.toString() ? `?${params.toString()}` : ''}`
    router.push(newUrl)

    // Realizar búsqueda con nuevos filtros
    if (query.trim()) {
      performSearch(query, updatedFilters)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-primary-green mb-4">
          Buscar en el Portal
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Encuentra rápidamente trámites, OPAs y servicios de la Alcaldía de Chía
        </p>
      </div>

      {/* Barra de búsqueda */}
      <div className="max-w-2xl mx-auto">
        <SearchBar
          initialValue={query}
          onSearch={handleSearch}
          placeholder="Buscar trámites, OPAs, servicios..."
          className="w-full"
        />
      </div>

      {/* Resultados */}
      <SearchResults
        results={results}
        loading={loading}
        error={error}
        query={query}
        total={total}
        onFilterChange={handleFilterChange}
      />

      {/* Información adicional */}
      {!query.trim() && (
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <h2 className="text-2xl font-bold text-primary-green mb-6 text-center">
            ¿Qué puedes buscar?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Trámites
              </h3>
              <p className="text-gray-600 text-sm">
                Certificados, licencias, permisos y todos los trámites municipales disponibles.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                OPAs
              </h3>
              <p className="text-gray-600 text-sm">
                Otras Prestaciones de Atención al ciudadano y servicios especiales.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Preguntas Frecuentes
              </h3>
              <p className="text-gray-600 text-sm">
                Respuestas a las preguntas más comunes sobre servicios municipales.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
