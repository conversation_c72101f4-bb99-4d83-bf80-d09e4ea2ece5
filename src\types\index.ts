import { Database } from './database'

// Tipos de las tablas
export type Dependencia = Database['public']['Tables']['dependencias']['Row']
export type Subdependencia = Database['public']['Tables']['subdependencias']['Row']
export type Tramite = Database['public']['Tables']['tramites']['Row']
export type Opa = Database['public']['Tables']['opas']['Row']
export type Faq = Database['public']['Tables']['faqs']['Row']
export type User = Database['public']['Tables']['users']['Row']

// Tipos para inserción
export type DependenciaInsert = Database['public']['Tables']['dependencias']['Insert']
export type SubdependenciaInsert = Database['public']['Tables']['subdependencias']['Insert']
export type TramiteInsert = Database['public']['Tables']['tramites']['Insert']
export type OpaInsert = Database['public']['Tables']['opas']['Insert']
export type FaqInsert = Database['public']['Tables']['faqs']['Insert']
export type UserInsert = Database['public']['Tables']['users']['Insert']

// Tipos para actualización
export type DependenciaUpdate = Database['public']['Tables']['dependencias']['Update']
export type SubdependenciaUpdate = Database['public']['Tables']['subdependencias']['Update']
export type TramiteUpdate = Database['public']['Tables']['tramites']['Update']
export type OpaUpdate = Database['public']['Tables']['opas']['Update']
export type FaqUpdate = Database['public']['Tables']['faqs']['Update']
export type UserUpdate = Database['public']['Tables']['users']['Update']

// Tipos extendidos con relaciones
export type TramiteWithRelations = Tramite & {
  subdependencia: Subdependencia & {
    dependencia: Dependencia
  }
}

export type OpaWithRelations = Opa & {
  subdependencia: Subdependencia & {
    dependencia: Dependencia
  }
}

export type FaqWithRelations = Faq & {
  dependencia: Dependencia
}

export type SubdependenciaWithRelations = Subdependencia & {
  dependencia: Dependencia
  tramites: Tramite[]
  opas: Opa[]
}

export type DependenciaWithRelations = Dependencia & {
  subdependencias: SubdependenciaWithRelations[]
  faqs: Faq[]
}

// Tipos para formularios
export type TramiteFormData = Omit<TramiteInsert, 'id' | 'created_at' | 'updated_at'>
export type OpaFormData = Omit<OpaInsert, 'id' | 'created_at' | 'updated_at'>
export type FaqFormData = Omit<FaqInsert, 'id' | 'created_at' | 'updated_at'>

// Tipos para búsqueda
export interface SearchFilters {
  query?: string
  dependencia_id?: string
  subdependencia_id?: string
  tiene_pago?: boolean
  activo?: boolean
}

export interface SearchResult {
  id: string
  tipo: 'tramite' | 'opa' | 'faq'
  titulo: string
  descripcion: string
  dependencia: string
  subdependencia?: string
  url: string
}

// Tipos para el chatbot
export interface ChatMessage {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
}

export interface ChatSession {
  id: string
  messages: ChatMessage[]
  created_at: Date
  updated_at: Date
}

// Tipos para estadísticas
export interface DashboardStats {
  total_tramites: number
  total_opas: number
  total_faqs: number
  total_dependencias: number
  consultas_mes: number
  tramites_populares: Array<{
    id: string
    nombre: string
    consultas: number
  }>
}
