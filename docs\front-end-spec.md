# Portal de Atención Ciudadana de Chía – UI/UX Specification

[[LLM: The default path and filename unless specified is docs/front-end-spec.md]]

## Introduction
Este documento define los objetivos de experiencia de usuario, arquitectura de información, flujos de usuario y especificaciones visuales para el Portal de Atención Ciudadana de Chía. Sirve como base para el diseño visual y el desarrollo frontend, asegurando una experiencia cohesionada y centrada en el usuario.

### Overall UX Goals & Principles
- Transformar la atención ciudadana con IA como pilar central.
- Automatizar y personalizar la experiencia del ciudadano.
- Facilitar autoservicio inteligente y escalamiento eficiente.
- <PERSON><PERSON><PERSON><PERSON> accesibilidad (WCAG AA), transparencia y mejora continua.
- Integrar un asistente de IA que reduzca el TMO de consultas ciudadanas.

### Target User Personas
- **Ciudadano:** Busca trámites y respuestas rápidas, requiere claridad y autoservicio.
- **Funcionario:** Gestiona trámites, OPAs y FAQs desde un panel administrativo seguro.
- **Administrador:** Supervisa usuarios, auditoría y configuración avanzada.

### Usability Goals
- Facilidad de aprendizaje: Nuevos usuarios completan tareas clave en menos de 5 minutos.
- Eficiencia: Usuarios frecuentes pueden realizar acciones en pocos clics.
- Prevención de errores: Validaciones claras y confirmaciones para acciones destructivas.
- Memorable: Usuarios ocasionales pueden volver y usar el sistema sin reaprender.

### Design Principles
1. **Claridad sobre complejidad**
2. **Divulgación progresiva**
3. **Patrones consistentes**
4. **Feedback inmediato**
5. **Accesibilidad por defecto**

### Change Log
| Date       | Version | Description                  | Author      |
| :--------- | :------ | :--------------------------- | :---------- |
| 2025-07-16 | 0.1     | Especificación inicial (MVP) | Equipo UX   |

## Information Architecture (IA)

### Site Map / Screen Inventory
```mermaid
graph TD
    A[Inicio] --> B[Trámites]
    A --> C[OPAs]
    A --> D[Centro de Ayuda]
    A --> E[Panel Funcionario]
    A --> F[Panel Admin]
    B --> B1[Detalle Trámite]
    C --> C1[Detalle OPA]
    D --> D1[FAQs]
    E --> E1[Gestión Trámites]
    E --> E2[Gestión OPAs]
    E --> E3[Gestión FAQs]
    F --> F1[Gestión Usuarios]
    F --> F2[Auditoría]
```

### Navigation Structure
- **Primaria:** Inicio, Trámites, OPAs, Centro de Ayuda, Panel Funcionario/Admin
- **Secundaria:** Búsqueda, Filtros, Acceso rápido a dependencias/subdependencias
- **Breadcrumbs:** Para navegación jerárquica en trámites y OPAs

## User Flows
### Ejemplo: Realizar un Trámite
- Usuario accede a "Trámites"
- Busca o filtra el trámite requerido
- Consulta requisitos y pasos
- Completa formulario y adjunta documentos
- Revisa y envía solicitud
- Recibe confirmación y seguimiento

### Edge Cases & Error Handling
- Validación de campos obligatorios
- Manejo de errores de red y respuestas de Supabase
- Mensajes claros ante errores o falta de información

## Visual Design & Branding
### Colores institucionales
```css
:root {
  --color-primary-yellow: #FFD C00;
  --color-primary-yellow-alt: #F8E000;
  --color-primary-green: #009045;
  --color-primary-green-alt: #009540;
}
```

### Tipografía
- **Principal:** Inter, Arial, sans-serif

### Iconografía
- Librería recomendada: Material Icons, Heroicons

### Espaciado y Layout
- Grid de 12 columnas
- Escala de espaciado: 4, 8, 16, 24, 32, 40px

### Breakpoints
| Breakpoint | Min Width | Max Width | Target Devices |
| :--------- | :-------- | :-------- | :------------- |
| Mobile     | 320px     | 767px     | Teléfonos      |
| Tablet     | 768px     | 1023px    | Tablets        |
| Desktop    | 1024px    | 1439px    | Laptops        |
| Wide       | 1440px    | -         | Monitores      |

## Accessibility Requirements
- Contraste de color mínimo AA
- Navegación por teclado
- Textos alternativos en imágenes
- Estructura de encabezados lógica
- Etiquetas claras en formularios

## Responsiveness Strategy
- Layout adaptativo para todos los dispositivos
- Menú responsive y accesible
- Priorización de contenido clave en móvil

## Animation & Micro-interactions
- Transiciones suaves en botones y formularios
- Feedback visual inmediato para acciones del usuario
- Animaciones accesibles (respetar preferencias de sistema)

## Performance Considerations
- Carga inicial < 2 segundos en 95% de los casos
- Lazy loading de recursos y componentes
- Optimización de imágenes y assets

## Next Steps
1. Revisar y validar con stakeholders
2. Crear prototipos visuales en Figma
3. Preparar handoff a desarrollo frontend
4. Actualizar este documento según feedback

## Design Handoff Checklist
- [ ] Todos los flujos de usuario documentados
- [ ] Inventario de componentes completo
- [ ] Requerimientos de accesibilidad definidos
- [ ] Estrategia responsive clara
- [ ] Guía de marca incorporada
- [ ] Objetivos de performance establecidos

---

## Wireframe Home Mejorado y Recomendaciones

### Wireframe Home Mejorado

```
--------------------------------------------------------------------------------
| [Logo Alcaldía de Chía]      | Servicios municipales al alcance de todos      |
| [Enlace: Dependencias] [Enlace: Trámites] [Enlace: OPAs] [Enlace: Ayuda]      |
| [Botón: Iniciar sesión] [Botón: Registrarse]                                  |
--------------------------------------------------------------------------------
| [Banner principal]                                                        |
|   "Servicios municipales al alcance de todos"                              |
|   "Accede a trámites, OPAs y ayuda en un solo lugar. El asistente virtual  |
|    te guía paso a paso."                                                   |
|   [Input: Buscar trámite, OPA, ayuda...] [Botón: Buscar]                   |
--------------------------------------------------------------------------------
| [Estadísticas rápidas: Trámites realizados | OPAs gestionadas | Tiempo medio |
| de respuesta | Atención 24/7]                                               |
--------------------------------------------------------------------------------

[Servicios más utilizados]
--------------------------------------------------------------------------------
| [Card: Solicitud de Certificado] [Card: Pago de Impuestos] [Card: Nueva OPA] |
| [Card: Consulta de Trámite] [Card: Preguntar al Asistente] [Card: Ver más]   |
--------------------------------------------------------------------------------

[¿Por qué elegir nuestro portal digital?]
--------------------------------------------------------------------------------
| [✔️] Autoservicio inteligente  [✔️] Accesible y seguro  [✔️] Soporte 24/7     |
--------------------------------------------------------------------------------

[Accesos rápidos por dependencia]
--------------------------------------------------------------------------------
| [Card: Atención al Ciudadano] [Card: Planeación] [Card: Hacienda] [Card: Ver todas] |
--------------------------------------------------------------------------------

[Preguntas Frecuentes]
--------------------------------------------------------------------------------
| [Accordion: ¿Cómo hago un trámite?] [Accordion: ¿Qué es una OPA?] [Más...]  |
| [Cada pregunta se expande para mostrar la respuesta clara y enlaces útiles]  |
--------------------------------------------------------------------------------

[Widget flotante: Asistente Virtual AI]
--------------------------------------------------------------------------------
| [🗨️] ¿Necesitas ayuda? Haz clic aquí para hablar con nuestro asistente.      |
| [El widget debe estar siempre visible en la esquina inferior derecha]        |
--------------------------------------------------------------------------------

[Footer]
--------------------------------------------------------------------------------
| ¿Necesitas ayuda adicional?                                                  |
| - Teléfono, correo, horarios de atención                                     |
| - [Botón: Enviar solicitud de soporte]                                       |
--------------------------------------------------------------------------------
| Enlaces útiles: Política de privacidad | Términos y condiciones | Redes       |
| sociales | © Alcaldía de Chía                                              |
--------------------------------------------------------------------------------
```

### Mejoras y recomendaciones adicionales

1. **Menú principal claro y accesible**
   - Incluye “Dependencias” como enlace destacado.
   - Botones de acceso rápido para login/registro.

2. **Buscador central y visible**
   - El input de búsqueda debe estar centrado y ser prominente, permitiendo encontrar trámites, OPAs o ayuda desde cualquier lugar.

3. **Cards de servicios y dependencias**
   - Usa tarjetas coloridas y accesibles para destacar los servicios más utilizados y accesos por dependencia.
   - El botón “Ver todas” en dependencias lleva a la página jerárquica de dependencias/subdependencias.

4. **Estadísticas y beneficios**
   - Muestra datos clave (trámites realizados, OPAs, tiempo de respuesta, atención 24/7) para generar confianza.
   - Resalta beneficios del portal digital.

5. **Preguntas frecuentes accesibles**
   - Usa acordeones para mostrar respuestas claras y enlaces directos a trámites o ayuda.

6. **Asistente AI siempre visible**
   - Widget flotante, accesible y con onboarding breve (“¿En qué puedo ayudarte?”).

7. **Footer informativo y útil**
   - Muestra datos de contacto, enlaces legales y acceso a soporte.

8. **Accesibilidad avanzada**
   - Contraste alto, navegación por teclado, textos alternativos en imágenes y botones.
   - Pruebas con usuarios reales, incluyendo personas mayores y con discapacidad.

9. **Optimización mobile-first**
   - Asegura que todos los bloques sean responsivos y prioritiza la información esencial en pantallas pequeñas.

10. **Performance y SEO**
    - Imágenes optimizadas, carga diferida de recursos, etiquetas meta completas y semántica HTML.

---

## Wireframe de Página de Dependencias (con códigos y totales)

```
--------------------------------------------------------------------------------
| [Logo Alcaldía de Chía]      | [Menú principal: Inicio | Dependencias | ... ] |
--------------------------------------------------------------------------------
| [Breadcrumb: Inicio > Dependencias]                                          |
--------------------------------------------------------------------------------
| Título: Dependencias municipales                                             |
| Descripción breve: Explora las áreas y servicios de la alcaldía.             |
--------------------------------------------------------------------------------

[Buscador de dependencias]
--------------------------------------------------------------------------------
| [Input: Buscar código o nombre de dependencia, subdependencia o trámite...] [Botón: Buscar]     |
--------------------------------------------------------------------------------

[Listado de dependencias]
--------------------------------------------------------------------------------
| [Card: Secretaría de Planeación]                                             |
|   Código: DEP-001                                                           |
|   Subdependencias: 3   |   Trámites: 12   |   OPAs: 5                        |
|   Descripción breve                                                         |
| [Botón: Ver detalle]                                                        |
| ...                                                                         |
--------------------------------------------------------------------------------

[Al hacer clic en una dependencia:]
--------------------------------------------------------------------------------
| [Breadcrumb: Inicio > Dependencias > Secretaría de Planeación (DEP-001)]     |
| Título: Secretaría de Planeación                                             |
| Código: DEP-001                                                              |
| Subdependencias: 3   |   Trámites: 12   |   OPAs: 5                          |
| Descripción de la dependencia                                                |
--------------------------------------------------------------------------------

[Subdependencias (desplegable o tabla)]
--------------------------------------------------------------------------------
| Subdependencia 1 (SUB-101)  | Trámites: 7 | OPAs: 2   [Ver trámites] [Ver OPAs] |
| Subdependencia 2 (SUB-102)  | Trámites: 5 | OPAs: 3   [Ver trámites] [Ver OPAs] |
| ...                                                                             |
--------------------------------------------------------------------------------

[Al hacer clic en “Ver trámites” o “Ver OPAs” de una subdependencia:]
--------------------------------------------------------------------------------
| [Breadcrumb: Inicio > Dependencias > Secretaría de Planeación (DEP-001) > Subdependencia 1 (SUB-101) > Trámites] |
| Título: Trámites de Subdependencia 1 (SUB-101)                              |
|                                                                              |
| [Tabla o cards de trámites:]                                                 |
|   - Código: TRA-0001 | Nombre: Solicitud de Certificado | Descripción | ...  |
|   - Código: TRA-0002 | Nombre: Renovación de Licencia   | Descripción | ...  |
| ...                                                                         |
--------------------------------------------------------------------------------
| [Tabla o cards de OPAs:]                                                     |
|   - Código: OPA-1001 | Nombre: Autorización de Pago | Descripción | ...      |
|   - Código: OPA-1002 | Nombre: Orden de Servicio    | Descripción | ...      |
| ...                                                                         |
--------------------------------------------------------------------------------

[Widget flotante: Asistente Virtual AI] (siempre visible)
--------------------------------------------------------------------------------
| [🗨️] ¿Tienes dudas sobre dependencias, códigos o trámites? Pregunta aquí.    |
--------------------------------------------------------------------------------

[Footer]
--------------------------------------------------------------------------------
| Contacto | Soporte | Términos y condiciones | Redes sociales | © Alcaldía Chía |
--------------------------------------------------------------------------------
```

### Recomendaciones adicionales para la vista de dependencias:
- Mostrar siempre los códigos únicos de dependencias, subdependencias, trámites y OPAs.
- Totalizar y mostrar el número de subdependencias, trámites y OPAs tanto en la card principal de cada dependencia como en el detalle.
- Al desplegar subdependencias, mostrar el total de trámites y OPAs por cada subdependencia.
- Permitir filtrar/buscar por código o nombre en todos los niveles.
- Breadcrumbs claros con códigos para orientación jerárquica.
- El asistente AI debe poder responder consultas usando códigos o nombres.
- Tablas o cards accesibles, legibles y fácilmente copiables.
- Diseño responsivo y accesible.


---

## Desglose de Componentes UI – Vista de Dependencias

### 1. DependenciasPage
- Componente de página principal. Orquesta el layout, breadcrumbs, buscador y listado.
- Props: ninguna directa (usa hooks de datos y contexto).

### 2. Breadcrumb
- Muestra la ruta de navegación jerárquica con códigos y nombres.
- Props: `items: [{ label: string, code?: string, href: string }]`

### 3. SearchBar
- Input para buscar dependencias, subdependencias o trámites por nombre o código.
- Props: `placeholder: string`, `onSearch: (query: string) => void`

### 4. DependenciaCard
- Card resumen de una dependencia.
- Props:
  - `nombre: string`
  - `codigo: string`
  - `descripcion: string`
  - `totalSubdependencias: number`
  - `totalTramites: number`
  - `totalOpas: number`
  - `onVerDetalle: () => void`

### 5. DependenciasGrid
- Grid responsivo de `DependenciaCard`.
- Props: `dependencias: Dependencia[]`

### 6. DependenciaDetail
- Vista detalle de una dependencia seleccionada.
- Props:
  - `nombre: string`
  - `codigo: string`
  - `descripcion: string`
  - `subdependencias: Subdependencia[]`
  - `totalTramites: number`
  - `totalOpas: number`

### 7. SubdependenciaRow
- Fila o card para cada subdependencia dentro del detalle.
- Props:
  - `nombre: string`
  - `codigo: string`
  - `totalTramites: number`
  - `totalOpas: number`
  - `onVerTramites: () => void`
  - `onVerOpas: () => void`

### 8. SubdependenciasTable
- Tabla o lista de subdependencias usando `SubdependenciaRow`.
- Props: `subdependencias: Subdependencia[]`

### 9. TramiteCard / OpaCard
- Card o fila para cada trámite/OPA.
- Props:
  - `codigo: string`
  - `nombre: string`
  - `descripcion: string`
  - Otros campos relevantes (estado, acciones, etc.)

### 10. TramitesList / OpasList
- Lista de trámites/OPAs para una subdependencia.
- Props: `items: Tramite[]` o `items: Opa[]`

### 11. FloatingAIAssistant
- Widget flotante de ayuda AI.
- Props: `context: string` (ej: dependencia actual)

### 12. Footer
- Pie de página institucional.

#### Ejemplo de Jerarquía de Componentes

```
<DependenciasPage>
  <Breadcrumb items=[...] />
  <SearchBar ... />
  <DependenciasGrid dependencias=[...] />
  <FloatingAIAssistant context='dependencias' />
  <Footer />
</DependenciasPage>
```
Al seleccionar una dependencia:
```
<DependenciaDetail ... >
  <SubdependenciasTable subdependencias=[...] />
</DependenciaDetail>
```

#### Recomendaciones adicionales:

- **La prioridad número uno es el diseño responsive/mobile-first:**
  - Todos los componentes y vistas deben funcionar perfectamente en dispositivos móviles y tablets, además de desktop.
  - Usa un sistema de grid fluido y breakpoints claros (ej: 320px, 768px, 1024px, 1440px).
  - El contenido esencial debe mostrarse primero y ser fácilmente navegable en pantallas pequeñas.
  - Menús, tablas y cards deben adaptarse (colapsar, apilarse o convertirse en sliders/lists) según el ancho de pantalla.
  - El buscador y botones clave deben ser grandes y fácilmente tocables.
  - Prueba con emuladores y dispositivos reales para asegurar experiencia óptima.
  - Considera gestos touch y scroll horizontal donde sea útil.
  - El widget AI debe ser accesible pero no invasivo en móvil.

- Todos los componentes deben ser accesibles (teclado, aria-labels, contraste).
- Usar CSS Modules o styled-components para estilos consistentes.
- Props bien tipadas (TypeScript recomendado).
- Reutilizar cards/tablas para trámites y OPAs.
- Testing con React Testing Library y Cypress para flujos críticos, incluyendo pruebas en mobile y tablet.

---

## Panel Backend: Gestión de Trámites, OPAs y FAQs

### Wireframe General

#### Dashboard Principal
```
--------------------------------------------------------------------------------
| [Logo Alcaldía de Chía]    | [Menú: Dashboard | Trámites | OPAs | FAQs | ...] |
--------------------------------------------------------------------------------
| [Usuario: Nombre | Rol: Funcionario/Admin | Cerrar sesión]                   |
--------------------------------------------------------------------------------
[Resumen rápido]
--------------------------------------------------------------------------------
| Trámites activos: 120 | OPAs activas: 45 | FAQs publicadas: 32 | Última edición: hoy |
--------------------------------------------------------------------------------
```

#### Gestión de Trámites
```
--------------------------------------------------------------------------------
| [Título: Gestión de Trámites]   [Botón: Nuevo Trámite]   [Buscador y Filtros] |
--------------------------------------------------------------------------------
| [Tabla de Trámites]                                                           |
| Código | Nombre | Dependencia | Estado | Fecha | Acciones (Editar | Desactivar) |
| TRA-0001 | Solicitud de Certificado | DEP-001 | Activo | 2025-07-16 | [✏️][⏻]   |
| TRA-0002 | Renovación de Licencia   | DEP-002 | Borrador | ...      | [✏️][⏻]   |
| ...                                                                          |
--------------------------------------------------------------------------------
| [Paginación]                                                                  |
--------------------------------------------------------------------------------
[Al hacer clic en “Nuevo Trámite” o “Editar”]
--------------------------------------------------------------------------------
| [Formulario de Trámite]                                                       |
| Código: [auto/generado]                                                       |
| Nombre: [___________]                                                         |
| Descripción: [___________]                                                    |
| Dependencia: [dropdown]                                                       |
| Subdependencia: [dropdown]                                                    |
| Requisitos: [textarea]                                                        |
| Pasos: [textarea]                                                             |
| Documentos requeridos: [multi-select]                                         |
| Estado: [Activo/Borrador/Inactivo]                                            |
| [Botón: Guardar] [Botón: Cancelar]                                            |
--------------------------------------------------------------------------------
```

#### Gestión de OPAs
```
--------------------------------------------------------------------------------
| [Título: Gestión de OPAs]   [Botón: Nueva OPA]   [Buscador y Filtros]         |
--------------------------------------------------------------------------------
| [Tabla de OPAs]                                                               |
| Código | Nombre | Dependencia | Estado | Fecha | Acciones (Editar | Desactivar)|
| OPA-1001 | Autorización de Pago | DEP-001 | Activo | ...      | [✏️][⏻]        |
| ...                                                                          |
--------------------------------------------------------------------------------
| [Formulario de OPA similar al de Trámite, con campos específicos]             |
--------------------------------------------------------------------------------
```

#### Gestión de FAQs
```
--------------------------------------------------------------------------------
| [Título: Gestión de FAQs]   [Botón: Nueva FAQ]   [Buscador y Filtros]         |
--------------------------------------------------------------------------------
| [Tabla de FAQs]                                                               |
| Código | Pregunta | Dependencia | Estado | Orden | Acciones (Editar | Desactivar)|
| FAQ-001 | ¿Cómo hago un trámite? | Global | Activo | 1 | [✏️][⏻]               |
| ...                                                                          |
--------------------------------------------------------------------------------
| [Formulario de FAQ]                                                           |
| Código: [auto/generado]                                                       |
| Pregunta: [___________]                                                       |
| Respuesta: [textarea]                                                         |
| Dependencia: [dropdown, opcional]                                             |
| Orden: [número]                                                               |
| Estado: [Activo/Borrador/Inactivo]                                            |
| [Botón: Guardar] [Botón: Cancelar]                                            |
--------------------------------------------------------------------------------
```

### Componentes UI principales para el panel de gestión

- **AdminDashboard**: Vista general con KPIs y accesos rápidos.
- **EntidadTable**: Tabla reutilizable para trámites, OPAs y FAQs.
  - Props: `data`, `columns`, `onEdit`, `onDeactivate`, `filters`, `pagination`
- **EntidadForm**: Formulario reutilizable para crear/editar trámites, OPAs o FAQs.
  - Props: `entidadType`, `initialValues`, `onSubmit`, `onCancel`
- **EntidadFilters**: Filtros avanzados por dependencia, estado, fecha, código, etc.
- **EntidadActions**: Botones de acción (editar, desactivar, historial).
- **Breadcrumb**: Ruta de navegación en el panel admin.
- **Modal**: Para formularios y confirmaciones.
- **UserMenu**: Info de usuario y logout.
- **Paginator**: Navegación de páginas en tablas largas.
- **AuditTrail**: Historial de cambios (opcional, para admins).

#### Características clave
- CRUD completo (crear, editar, desactivar, buscar, filtrar, paginar).
- Validaciones y mensajes de error claros.
- Accesible y responsive (usable en tablet/laptop).
- Seguridad: solo roles autorizados acceden.
- Auditoría de cambios.

---

> Este documento debe evolucionar junto con el producto y el feedback de usuarios y stakeholders.
