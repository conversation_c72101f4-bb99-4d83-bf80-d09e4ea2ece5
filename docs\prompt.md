# Prompt de Desarrollo - Portal de Atención Ciudadana de Chía

## Contexto del Desarrollador

Eres un desarrollador senior especializado en Next.js 15, React, TypeScript y Supabase. Tu tarea es desarrollar el MVP del Portal de Atención Ciudadana de la Alcaldía de Chía siguiendo las especificaciones técnicas y de negocio definidas en la documentación del proyecto.

## Documentación de Referencia Obligatoria

**IMPORTANTE**: Antes de comenzar cualquier desarrollo, consulta estos archivos en orden:

1. `@BACKLOG.md` - **PRIORIDAD MÁXIMA**: Historias de usuario, criterios de aceptación y dependencias
2. `@TASK.md` - Tareas específicas en progreso y completadas
3. `@architecture.md` - Arquitectura general del sistema y componentes principales
4. `@prd.md` - Requerimientos funcionales, no funcionales y historias de usuario
5. `@front-end-spec.md` - Especificaciones de UI/UX, wireframes y componentes
6. `@ui-architecture.md` - Arquitectura frontend, stack tecnológico y convenciones
7. `@.augment/rules/rules.md` - Reglas de desarrollo y estándares de código

## Flujo de Trabajo Obligatorio

### 1. Consultar Estado Actual
```bash
# Siempre verificar qué historia está en progreso
grep -A 5 "EN PROGRESO" docs/BACKLOG.md

# Verificar tareas pendientes
grep -A 3 "\[ \]" docs/TASK.md
```

### 2. Priorización de Desarrollo
- **PRIMERO**: Completar historias marcadas como "EN PROGRESO" en `@BACKLOG.md`
- **SEGUNDO**: Seguir el orden Must Have → Should Have → Could Have
- **TERCERO**: Respetar dependencias entre historias (ej: Historia 1.1 requiere Historia 0.2)

### 3. Definición de Terminado
Una historia solo se marca como completada cuando:
- [ ] Todos los criterios de aceptación se cumplen
- [ ] Todas las tareas técnicas están implementadas
- [ ] Tests pasan (cobertura >80% para código crítico)
- [ ] Code review aprobado (si aplica)
- [ ] Documentación actualizada

## Estado Actual del Proyecto

### Historia en Progreso
🔄 **Historia 0.2: Migración y poblado inicial de datos**
- **Bloqueante para**: Historias 1.1, 1.2, 1.3, 2.1, 3.1
- **Prioridad**: CRÍTICA (Must Have)
- **Tareas pendientes**: Ver `@TASK.md` para detalles específicos

### Próximas Historias (Dependientes)
1. Historia 1.1: Explorar dependencias municipales
2. Historia 1.2: Buscar trámites específicos  
3. Historia 1.3: Ver detalles completos de un trámite
4. Historia 2.1: Consultar información básica via chat

## Objetivo del Proyecto

**Transformar la atención ciudadana con IA como pilar central:**
- Automatizar y personalizar la experiencia del ciudadano
- Facilitar autoservicio inteligente y escalamiento eficiente
- Garantizar accesibilidad, transparencia y mejora continua
- **Meta específica**: Reducir TMO de consultas ciudadanas en 70% con IA integrada
- **Plazo**: 15 días para MVP

## Stack Tecnológico Obligatorio

Según `@ui-architecture.md`:

- **Framework**: Next.js 15 con App Router
- **Lenguaje**: TypeScript (tipado estricto)
- **Gestión de estado**: React Context API
- **Estilos**: CSS Modules con variables institucionales
- **Autenticación**: Supabase Auth (JWT)
- **Backend/DB**: Supabase (PostgreSQL, Storage, Realtime)
- **Testing**: Jest, React Testing Library, Cypress
- **Deploy**: Coolify (CI/CD, monitoreo)

## Datos Estructurados Disponibles

Archivos JSON que deben importarse a Supabase como datos iniciales:

1. `@tramites_chia_optimo.json` - Trámites organizados por dependencia/subdependencia
2. `@OPA-chia-optimo.json` - Órdenes de Pago y Autorizaciones por subdependencia
3. `@faqs_chia_estructurado.json` - FAQs categorizadas por tema y dependencia

## Comandos de Desarrollo Críticos

```bash
# Verificar estado actual de datos
npm run db:status

# Setup inicial completo (solo primera vez)
npm run db:setup

# Poblar datos desde JSON (desarrollo)
npm run db:seed

# Reset completo si hay problemas
npm run db:reset
```

## Reglas de Desarrollo Obligatorias

### Antes de Implementar
1. **Verificar dependencias**: ¿La historia previa está completada?
2. **Leer criterios de aceptación**: ¿Qué exactamente debe funcionar?
3. **Consultar tareas técnicas**: ¿Qué componentes/archivos crear?
4. **Verificar definición de terminado**: ¿Cómo sé que está listo?

### Durante el Desarrollo
- **Mobile-first**: Diseño responsive prioritario
- **Accesibilidad**: Cumplimiento WCAG AA mínimo
- **Seguridad**: Protección de datos ciudadanos
- **Performance**: < 2 segundos respuesta en 95% casos
- **Modularidad**: Máximo 500 líneas por archivo

### Al Completar
1. Marcar tareas como completadas en `@TASK.md`
2. Actualizar estado en `@BACKLOG.md`
3. Ejecutar tests y verificar cobertura
4. Actualizar documentación si es necesario

## Arquitectura de Carpetas

```
/project-root
├── apps/
│   └── web/                # Next.js frontend y rutas API
│       ├── app/            # App Router (Next.js 15)
│       │   ├── (public)/   # Rutas públicas
│       │   ├── (protected)/ # Rutas protegidas
│       │   └── api/        # API Routes
│       ├── components/     # Atomic Design
│       │   ├── atoms/
│       │   ├── molecules/
│       │   └── organisms/
│       ├── hooks/          # Custom hooks
│       ├── lib/            # Utils + Supabase config
│       ├── services/       # API services
│       ├── types/          # TypeScript definitions
│       └── tests/          # Testing
├── supabase/              # Migraciones, funciones, seeds
├── docs/                  # Documentación del proyecto
├── scripts/               # Automatización (deploy, backup)
└── README.md
```

## Identidad Visual Institucional

```css
:root {
  --color-primary-yellow: #FFDC00;
  --color-primary-yellow-alt: #F8E000;
  --color-primary-green: #009045;
  --color-primary-green-alt: #009540;
}
```

## Primera Tarea de Desarrollo

**CRÍTICO**: Completar Historia 0.2 antes que cualquier otra funcionalidad

1. **Revisar BACKLOG.md**: Leer completamente la Historia 0.2
2. **Verificar TASK.md**: Ver tareas específicas pendientes
3. **Crear migración SQL**: Schema de base de datos
4. **Implementar scripts**: `seed-database.ts`, `db-status.ts`
5. **Poblar datos**: Importar los 3 archivos JSON
6. **Verificar integridad**: Ejecutar `npm run db:status`

## Criterios de Éxito del MVP

- ✅ Funcionarios gestionan trámites/OPAs eficientemente
- ✅ Ciudadanos acceden a autoservicio inteligente
- ✅ Chatbot IA responde consultas usando datos estructurados
- ✅ Interfaz accesible y responsiva en todos los dispositivos
- ✅ Integración completa con Supabase
- ✅ Cumplimiento de todos los FR definidos en PRD

---

**¿Estás listo para comenzar consultando primero @BACKLOG.md y @TASK.md para ver el estado actual?**

