import { Suspense } from 'react'
import { Metadata } from 'next'
import DependenciasGrid from '@/components/organisms/DependenciasGrid'
import Loading from '@/components/atoms/Loading'

export const metadata: Metadata = {
  title: 'Dependencias Municipales - Portal Ciudadano Chía',
  description: 'Explora todas las dependencias de la Alcaldía de Chía y encuentra los servicios que necesitas.',
  keywords: ['dependencias', 'alcaldía', 'chía', 'servicios', 'trámites'],
}

interface DependenciasPageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default function DependenciasPage({ searchParams }: DependenciasPageProps) {
  const searchQuery = typeof searchParams.q === 'string' ? searchParams.q : ''

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-custom py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Dependencias Municipales
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Conoce todas las dependencias de la Alcaldía de Chía y los servicios que ofrecen. 
            Encuentra rápidamente la dependencia que maneja el trámite que necesitas.
          </p>
        </div>

        {/* Barra de búsqueda */}
        <div className="max-w-2xl mx-auto mb-8">
          <SearchForm initialQuery={searchQuery} />
        </div>

        {/* Grid de dependencias */}
        <Suspense fallback={<DependenciasGridSkeleton />}>
          <DependenciasGrid searchQuery={searchQuery} />
        </Suspense>

        {/* Información adicional */}
        <div className="mt-16 bg-white rounded-lg shadow-sm p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Organización Clara
              </h3>
              <p className="text-gray-600 text-sm">
                Cada dependencia está organizada con sus subdependencias y servicios específicos.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Búsqueda Fácil
              </h3>
              <p className="text-gray-600 text-sm">
                Encuentra rápidamente la dependencia que necesitas usando nuestra búsqueda inteligente.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-yellow rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-primary-green mb-2">
                Información Completa
              </h3>
              <p className="text-gray-600 text-sm">
                Accede a información detallada sobre servicios, trámites y preguntas frecuentes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function SearchForm({ initialQuery }: { initialQuery: string }) {
  return (
    <form method="GET" className="relative">
      <input
        type="text"
        name="q"
        defaultValue={initialQuery}
        placeholder="Buscar dependencias por nombre..."
        className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-yellow focus:border-transparent text-lg"
      />
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <button
        type="submit"
        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-green text-white px-4 py-2 rounded-md hover:bg-primary-green-alt transition-colors"
      >
        Buscar
      </button>
    </form>
  )
}

function DependenciasGridSkeleton() {
  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-48 mx-auto"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-md p-6">
            <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-16 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="h-12 bg-gray-100 rounded"></div>
              <div className="h-12 bg-gray-100 rounded"></div>
            </div>
            <div className="flex gap-2">
              <div className="h-8 bg-gray-200 rounded flex-1"></div>
              <div className="h-8 bg-gray-200 rounded flex-1"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
