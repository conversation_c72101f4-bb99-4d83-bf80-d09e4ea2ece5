-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum for user roles
CREATE TYPE user_role AS ENUM ('ciudadano', 'funcionario', 'admin');

-- Create users table (extends auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    nombre TEXT,
    rol user_role DEFAULT 'ciudadano',
    dependencia_id UUID,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create dependencias table
CREATE TABLE public.dependencias (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    codigo TEXT NOT NULL UNIQUE,
    nombre TEXT NOT NULL,
    sigla TEXT,
    descripcion TEXT,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subdependencias table
CREATE TABLE public.subdependencias (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    codigo TEXT NOT NULL UNIQUE,
    nombre TEXT NOT NULL,
    sigla TEXT,
    dependencia_id UUID NOT NULL REFERENCES public.dependencias(id) ON DELETE CASCADE,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tramites table
CREATE TABLE public.tramites (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    codigo_unico TEXT NOT NULL UNIQUE,
    nombre TEXT NOT NULL,
    formulario TEXT,
    tiempo_respuesta TEXT,
    tiene_pago BOOLEAN DEFAULT false,
    visualizacion_suit TEXT,
    visualizacion_gov TEXT,
    subdependencia_id UUID NOT NULL REFERENCES public.subdependencias(id) ON DELETE CASCADE,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create opas table
CREATE TABLE public.opas (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    codigo_opa TEXT NOT NULL UNIQUE,
    nombre TEXT NOT NULL,
    subdependencia_id UUID NOT NULL REFERENCES public.subdependencias(id) ON DELETE CASCADE,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create faqs table
CREATE TABLE public.faqs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    pregunta TEXT NOT NULL,
    respuesta TEXT NOT NULL,
    palabras_clave TEXT[] DEFAULT '{}',
    dependencia_id UUID NOT NULL REFERENCES public.dependencias(id) ON DELETE CASCADE,
    tema TEXT,
    activo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for users.dependencia_id
ALTER TABLE public.users 
ADD CONSTRAINT fk_users_dependencia 
FOREIGN KEY (dependencia_id) REFERENCES public.dependencias(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX idx_subdependencias_dependencia_id ON public.subdependencias(dependencia_id);
CREATE INDEX idx_tramites_subdependencia_id ON public.tramites(subdependencia_id);
CREATE INDEX idx_opas_subdependencia_id ON public.opas(subdependencia_id);
CREATE INDEX idx_faqs_dependencia_id ON public.faqs(dependencia_id);
CREATE INDEX idx_users_dependencia_id ON public.users(dependencia_id);
CREATE INDEX idx_users_rol ON public.users(rol);

-- Create indexes for search functionality
CREATE INDEX idx_tramites_nombre ON public.tramites USING gin(to_tsvector('spanish', nombre));
CREATE INDEX idx_opas_nombre ON public.opas USING gin(to_tsvector('spanish', nombre));
CREATE INDEX idx_faqs_pregunta ON public.faqs USING gin(to_tsvector('spanish', pregunta));
CREATE INDEX idx_faqs_respuesta ON public.faqs USING gin(to_tsvector('spanish', respuesta));
CREATE INDEX idx_faqs_palabras_clave ON public.faqs USING gin(palabras_clave);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dependencias_updated_at BEFORE UPDATE ON public.dependencias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subdependencias_updated_at BEFORE UPDATE ON public.subdependencias FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tramites_updated_at BEFORE UPDATE ON public.tramites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_opas_updated_at BEFORE UPDATE ON public.opas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_faqs_updated_at BEFORE UPDATE ON public.faqs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
