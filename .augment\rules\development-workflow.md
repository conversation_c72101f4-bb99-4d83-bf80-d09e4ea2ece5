---
type: "always_apply"
priority: "critical"
---

# Portal Ciudadano Chía - Flujo de Desarrollo Obligatorio

## Contexto del Proyecto
Desarrollas el MVP del Portal de Atención Ciudadana de la Alcaldía de Chía usando Next.js 15, React, TypeScript y Supabase. Meta: Reducir TMO de consultas ciudadanas en 70% con IA integrada. Plazo: 15 días.

## Documentación de Referencia (Orden Obligatorio)
1. `@BACKLOG.md` - **PRIORIDAD MÁXIMA**: Historias, criterios, dependencias
2. `@TASK.md` - Tareas específicas en progreso
3. `@architecture.md` - Arquitectura del sistema
4. `@prd.md` - Requerimientos funcionales
5. `@front-end-spec.md` - Especificaciones UI/UX
6. `@ui-architecture.md` - Stack tecnológico

## Flujo de Trabajo Obligatorio

### ANTES de cualquier desarrollo:
```bash
# 1. Verificar historia en progreso
grep -A 5 "EN PROGRESO" docs/BACKLOG.md

# 2. Ver tareas pendientes
grep -A 3 "\[ \]" docs/TASK.md
```

### Priorización:
1. **PRIMERO**: Completar historias "EN PROGRESO" en BACKLOG.md
2. **SEGUNDO**: Must Have → Should Have → Could Have
3. **TERCERO**: Respetar dependencias (ej: Historia 1.1 requiere Historia 0.2)

### Estado Actual Crítico:
🔄 **Historia 0.2: Migración y poblado inicial de datos** (EN PROGRESO)
- **Bloqueante para**: Historias 1.1, 1.2, 1.3, 2.1, 3.1
- **Archivos JSON**: `tramites_chia_optimo.json`, `OPA-chia-optimo.json`, `faqs_chia_estructurado.json`

## Stack Tecnológico Obligatorio
- **Framework**: Next.js 15 con App Router
- **Lenguaje**: TypeScript (tipado estricto)
- **Base de datos**: Supabase (PostgreSQL + RLS)
- **Estilos**: CSS Modules + variables institucionales
- **Testing**: Jest + React Testing Library

## Comandos Críticos
```bash
npm run db:status    # Verificar estado de datos
npm run db:setup     # Setup inicial completo
npm run db:seed      # Poblar datos desde JSON
npm run db:reset     # Reset completo
```

## Reglas de Desarrollo

### Antes de Implementar:
- [ ] ¿La historia previa está completada?
- [ ] ¿Qué criterios de aceptación debo cumplir?
- [ ] ¿Qué tareas técnicas específicas hay?
- [ ] ¿Cómo sé que está terminado?

### Durante el Desarrollo:
- **Mobile-first**: Diseño responsive prioritario
- **Accesibilidad**: WCAG AA mínimo
- **Performance**: < 2 segundos respuesta
- **Modularidad**: Máximo 500 líneas por archivo
- **Seguridad**: Protección datos ciudadanos

### Al Completar:
1. Marcar tareas en `TASK.md`
2. Actualizar estado en `BACKLOG.md`
3. Ejecutar tests (cobertura >80%)
4. Actualizar documentación

## Arquitectura de Carpetas
```
/project-root
├── apps/web/           # Next.js frontend
│   ├── app/           # App Router
│   ├── components/    # Atomic Design
│   ├── hooks/         # Custom hooks
│   ├── lib/           # Utils + Supabase
│   └── services/      # API services
├── supabase/          # Migraciones, seeds
└── docs/              # Documentación
```

## Identidad Visual
```css
:root {
  --color-primary-yellow: #FFDC00;
  --color-primary-green: #009045;
}
```

## Definición de Terminado
Una historia se marca completada cuando:
- [ ] Todos los criterios de aceptación se cumplen
- [ ] Todas las tareas técnicas implementadas
- [ ] Tests pasan (cobertura >80% código crítico)
- [ ] Documentación actualizada

## Primera Tarea Crítica
**COMPLETAR Historia 0.2** antes que cualquier otra funcionalidad:
1. Crear migración SQL con schema completo
2. Implementar scripts `seed-database.ts` y `db-status.ts`
3. Poblar datos desde los 3 archivos JSON
4. Verificar integridad con `npm run db:status`