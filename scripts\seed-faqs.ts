#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import { Database } from '../src/types/database'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

interface FaqJSON {
  pregunta: string
  respuesta: string
  palabras_clave: string[]
}

interface TemaJSON {
  tema: string
  descripcion: string
  preguntas_frecuentes: FaqJSON[]
}

interface FaqDependenciaJSON {
  dependencia: string
  codigo_dependencia: string
  subdependencia: string
  codigo_subdependencia: string
  temas: TemaJSON[]
}

interface FaqsData {
  faqs: FaqDependenciaJSON[]
}

async function seedFaqs() {
  console.log('❓ Importando solo FAQs...')
  
  // Limpiar FAQs existentes
  await supabase.from('faqs').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  
  const faqsPath = join(process.cwd(), 'docs', 'faqs_chia_estructurado.json')
  const faqsData: FaqsData = JSON.parse(readFileSync(faqsPath, 'utf-8'))
  
  let faqsCount = 0
  let dependenciasEncontradas = 0
  let temasCount = 0
  
  for (const faqDep of faqsData.faqs) {
    // Buscar la dependencia existente
    const { data: depData } = await supabase
      .from('dependencias')
      .select('id, nombre')
      .eq('codigo', faqDep.codigo_dependencia)
      .single()
    
    if (!depData) {
      console.warn(`⚠️ Dependencia ${faqDep.codigo_dependencia} (${faqDep.dependencia}) no encontrada. Asegúrate de ejecutar seed-tramites.ts primero.`)
      continue
    }
    
    dependenciasEncontradas++
    console.log(`✅ Procesando dependencia: ${depData.nombre}`)
    
    for (const tema of faqDep.temas) {
      temasCount++
      console.log(`   📝 Procesando tema: ${tema.tema}`)
      
      for (const faq of tema.preguntas_frecuentes) {
        const { error: faqError } = await supabase
          .from('faqs')
          .insert({
            pregunta: faq.pregunta,
            respuesta: faq.respuesta,
            palabras_clave: faq.palabras_clave,
            dependencia_id: depData.id,
            tema: tema.tema
          })
        
        if (faqError) {
          console.error(`❌ Error insertando FAQ "${faq.pregunta}":`, faqError)
          continue
        }
        
        faqsCount++
      }
      
      console.log(`      ✅ ${tema.preguntas_frecuentes.length} FAQs insertadas`)
    }
  }
  
  console.log(`🎉 FAQs importadas exitosamente:`)
  console.log(`   - ${dependenciasEncontradas} dependencias procesadas`)
  console.log(`   - ${temasCount} temas procesados`)
  console.log(`   - ${faqsCount} FAQs insertadas`)
}

async function main() {
  try {
    await seedFaqs()
  } catch (error) {
    console.error('❌ Error durante la importación:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
