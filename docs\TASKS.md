# Lista de Tareas - Portal Ciudadano Chía

## Estado de Tareas
- 🟡 **Pendiente** - No iniciada
- 🔵 **En Progreso** - En desarrollo
- 🟢 **Completada** - Finalizada y probada
- 🔴 **Bloqueada** - Requiere dependencia externa
- ⚠️ **Revisión** - Necesita code review

---

## FASE 1: Configuración Base (Días 1-2)

### 1.1 Setup Inicial del Proyecto
- [ ] 🟡 Crear estructura de carpetas según `@ui-architecture.md`
- [ ] 🟡 Configurar Next.js 15 con App Router
- [ ] 🟡 Instalar dependencias (Supabase, TypeScript, testing)
- [ ] 🟡 Configurar variables de entorno (.env.local)
- [ ] 🟡 Setup ESLint, Prettier y configuración TypeScript

### 1.2 Configuración Supabase
- [ ] 🟡 Crear proyecto Supabase
- [ ] 🟡 Configurar cliente Supabase en `/lib/supabase.ts`
- [ ] 🟡 Definir tipos TypeScript base en `/types/index.ts`
- [ ] 🟡 Crear esquema de base de datos (migraciones)
- [ ] 🟡 Configurar Row Level Security (RLS)

### 1.3 Estilos y Layout Base
- [ ] 🟡 Implementar variables CSS institucionales en `globals.css`
- [ ] 🟡 Crear layout principal (`app/layout.tsx`)
- [ ] 🟡 Implementar componentes base (Header, Footer)
- [ ] 🟡 Configurar CSS Modules

---

## FASE 2: Importación de Datos (Días 2-3)

### 2.1 Migración de Datos JSON
- [ ] 🟡 Script importación `tramites_chia_optimo.json`
- [ ] 🟡 Script importación `OPA-chia-optimo.json`
- [ ] 🟡 Script importación `faqs_chia_estructurado.json`
- [ ] 🟡 Validación de integridad de datos importados
- [ ] 🟡 Seeds para datos de prueba

### 2.2 Servicios de API
- [ ] 🟡 Servicio para dependencias (`/services/dependenciasApi.ts`)
- [ ] 🟡 Servicio para trámites (`/services/tramitesApi.ts`)
- [ ] 🟡 Servicio para OPAs (`/services/opasApi.ts`)
- [ ] 🟡 Servicio para FAQs (`/services/faqsApi.ts`)

---

## FASE 3: Componentes Core (Días 3-5)

### 3.1 Componentes Atómicos
- [ ] 🟡 `Button` component con variantes
- [ ] 🟡 `Input` component con validación
- [ ] 🟡 `Card` component reutilizable
- [ ] 🟡 `Badge` component para estados
- [ ] 🟡 `Loading` component y skeletons

### 3.2 Componentes Moleculares
- [ ] 🟡 `SearchBar` con autocompletado
- [ ] 🟡 `Breadcrumb` navegación jerárquica
- [ ] 🟡 `Pagination` component
- [ ] 🟡 `Filter` component avanzado
- [ ] 🟡 `Modal` component base

### 3.3 Componentes Organismos
- [ ] 🟡 `DependenciasGrid` - Grid de dependencias
- [ ] 🟡 `DependenciaCard` - Tarjeta individual
- [ ] 🟡 `TramitesList` - Lista de trámites
- [ ] 🟡 `OpasList` - Lista de OPAs
- [ ] 🟡 `FAQSection` - Sección de preguntas frecuentes

---

## FASE 4: Páginas Públicas (Días 5-7)

### 4.1 Página Principal
- [ ] 🟡 Diseño homepage con accesos rápidos
- [ ] 🟡 Estadísticas en tiempo real
- [ ] 🟡 Servicios más utilizados
- [ ] 🟡 Integración con búsqueda global

### 4.2 Vista de Dependencias
- [ ] 🟡 Página listado dependencias (`/app/(public)/dependencias/page.tsx`)
- [ ] 🟡 Página detalle dependencia (`/app/(public)/dependencias/[codigo]/page.tsx`)
- [ ] 🟡 Vista subdependencias con trámites/OPAs
- [ ] 🟡 Filtrado y búsqueda por código/nombre

### 4.3 Vista de Trámites
- [ ] 🟡 Página listado trámites (`/app/(public)/tramites/page.tsx`)
- [ ] 🟡 Página detalle trámite (`/app/(public)/tramites/[codigo]/page.tsx`)
- [ ] 🟡 Filtros por dependencia, tiempo, costo
- [ ] 🟡 Enlaces a formularios y visualización

### 4.4 Centro de Ayuda
- [ ] 🟡 Página FAQs (`/app/(public)/faqs/page.tsx`)
- [ ] 🟡 Búsqueda en FAQs por palabras clave
- [ ] 🟡 Categorización por dependencia/tema
- [ ] 🟡 Integración con chatbot

---

## FASE 5: Autenticación y Rutas Protegidas (Días 7-9)

### 5.1 Sistema de Autenticación
- [ ] 🟡 Configurar Supabase Auth
- [ ] 🟡 Componente `AuthProvider` con Context
- [ ] 🟡 Hook `useAuth` personalizado
- [ ] 🟡 Componente `AuthGuard` para protección de rutas
- [ ] 🟡 Páginas login/registro

### 5.2 Panel Funcionarios
- [ ] 🟡 Layout panel admin (`/app/(protected)/admin/layout.tsx`)
- [ ] 🟡 Dashboard funcionarios con KPIs
- [ ] 🟡 Gestión de trámites (CRUD)
- [ ] 🟡 Gestión de OPAs (CRUD)
- [ ] 🟡 Gestión de FAQs (CRUD)

### 5.3 Panel Administrador
- [ ] 🟡 Gestión de usuarios y roles
- [ ] 🟡 Dashboard de estadísticas avanzadas
- [ ] 🟡 Logs de auditoría
- [ ] 🟡 Configuración del sistema

---

## FASE 6: Chatbot IA (Días 9-11)

### 6.1 Componente Chatbot
- [ ] 🟡 `FloatingAIAssistant` widget flotante
- [ ] 🟡 Interfaz de chat responsive
- [ ] 🟡 Estados de carga y error
- [ ] 🟡 Historial de conversación

### 6.2 Integración con Datos
- [ ] 🟡 API endpoint para consultas IA (`/app/api/chat/route.ts`)
- [ ] 🟡 Conexión con datos Supabase
- [ ] 🟡 Procesamiento de consultas sobre trámites/OPAs
- [ ] 🟡 Respuestas contextuales por dependencia

### 6.3 Funcionalidades Avanzadas
- [ ] 🟡 Escalado a humano (opcional)
- [ ] 🟡 Sugerencias automáticas
- [ ] 🟡 Accesibilidad (navegación por teclado)
- [ ] 🟡 Soporte multiidioma básico

---

## FASE 7: Testing y Optimización (Días 11-13)

### 7.1 Testing Unitario
- [ ] 🟡 Tests para componentes atómicos
- [ ] 🟡 Tests para hooks personalizados
- [ ] 🟡 Tests para servicios API
- [ ] 🟡 Tests para utilidades

### 7.2 Testing de Integración
- [ ] 🟡 Tests flujo navegación dependencias
- [ ] 🟡 Tests flujo búsqueda y filtrado
- [ ] 🟡 Tests autenticación y autorización
- [ ] 🟡 Tests chatbot básico

### 7.3 Optimización
- [ ] 🟡 Optimización de imágenes y assets
- [ ] 🟡 Code splitting y lazy loading
- [ ] 🟡 SEO básico y meta tags
- [ ] 🟡 Performance audit con Lighthouse

---

## FASE 8: Deploy y Documentación (Días 13-15)

### 8.1 Preparación Deploy
- [ ] 🟡 Configuración Coolify
- [ ] 🟡 Variables de entorno producción
- [ ] 🟡 Pipeline CI/CD
- [ ] 🟡 Configuración dominios

### 8.2 Documentación
- [ ] 🟡 README.md completo
- [ ] 🟡 Documentación API endpoints
- [ ] 🟡 Guía de instalación y desarrollo
- [ ] 🟡 Manual de usuario básico

### 8.3 Testing Final
- [ ] 🟡 Testing en dispositivos móviles reales
- [ ] 🟡 Testing de accesibilidad (WCAG AA)
- [ ] 🟡 Testing de carga básico
- [ ] 🟡 Validación criterios de éxito

---

## Tareas Bloqueadas / Dependencias

- [ ] 🔴 **Configuración servidor Coolify** - Requiere acceso infraestructura
- [ ] 🔴 **Integración IA externa** - Definir proveedor (OpenAI, Claude, etc.)
- [ ] 🔴 **Configuración dominio producción** - Requiere DNS municipal

---

## Notas de Desarrollo

### Decisiones Técnicas Pendientes
- [ ] Definir proveedor de IA para chatbot
- [ ] Confirmar límites de rate limiting Supabase
- [ ] Validar requerimientos de accesibilidad específicos

### Riesgos Identificados
- **Alto**: Integración chatbot IA puede requerir más tiempo
- **Medio**: Importación masiva de datos JSON puede afectar performance
- **Bajo**: Configuración RLS compleja para multi-rol

---

## Progreso General
- **Completadas**: 0/X tareas
- **En progreso**: 0/X tareas  
- **Pendientes**: X/X tareas
- **% Completado**: 0%

---

*Última actualización: [Fecha]*
*Próxima revisión: [Fecha]*