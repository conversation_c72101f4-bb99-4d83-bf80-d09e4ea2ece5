# Portal de Atención Ciudadana de Chía – Frontend Architecture Document

## 1. Introducción
Este documento define la arquitectura frontend para el MVP del Portal de Atención Ciudadana de Chía, alineado con los requerimientos funcionales, técnicos y de experiencia de usuario definidos en el PRD y documentos de arquitectura.

## 2. Stack Tecnológico
- **Framework:** Next.js (React)
- **Lenguaje:** TypeScript
- **Gestión de estado:** React Context API (ampliable a Zustand/Redux si crece la complejidad)
- **Estilos:** CSS Modules (se recomienda futura integración con Tailwind)
- **Autenticación:** Supabase Auth (JWT)
- **Backend y BDD:** Supabase (PostgreSQL, Storage, Realtime)
- **Testing:** Jest, React Testing Library, Cypress
- **Despliegue:** Coolify (CI/CD, monitoreo)
- **AI:** Asistente virtual integrado conectado a Supabase y archivos JSON estructurados

## 3. Estructura de Carpetas Recomendada
```
/project-root
├── apps
│   └── web                # Next.js frontend y rutas API
├── supabase               # Migraciones, funciones, seeds
├── docs                   # Documentación y especificaciones
├── scripts                # Automatización (deploy, backup, etc.)
├── .env*
├── package.json
└── README.md
```

## 4. Variables de Color Institucionales
Utiliza estos colores como base para el sistema de diseño y las variables globales CSS:

```css
:root {
  --color-primary-yellow: #FFD C00;
  --color-primary-yellow-alt: #F8E000;
  --color-primary-green: #009045;
  --color-primary-green-alt: #009540;
}
```

## 5. Convenciones y Componentización
- Componentes en `/components` siguiendo Atomic Design (Atoms, Molecules, Organisms).
- Hooks personalizados en `/hooks`.
- Servicios de API en `/services`.
- Nomenclatura PascalCase para componentes y hooks.
- Archivos de estilos junto a cada componente (`Component.module.css`).

## 6. Integración del Asistente AI
- Widget de chat visible en toda la app (botón flotante).
- El asistente consume datos de Supabase y archivos JSON estructurados para responder consultas.
- Escalado a humano disponible si el AI no resuelve la consulta.
- Accesibilidad: soporte de navegación por teclado y lectores de pantalla.

## 7. Accesibilidad y Buenas Prácticas
- Cumplimiento mínimo WCAG AA.
- Contraste adecuado, navegación por teclado, textos alternativos en imágenes.
- Soporte multilingüe y modo lectura fácil.

## 8. Testing y Calidad
- Unit tests para componentes (Jest + React Testing Library).
- E2E tests para flujos críticos (Cypress).
- Linter y formateo automático (ESLint, Prettier).
- Pipeline CI/CD con Coolify.

## 9. Developer Experience
- Scripts de setup y migración.
- Documentación clara en `/docs`.
- Entorno local reproducible (docker-compose opcional).
- Uso de ramas y PRs para control de calidad.

## 10. Referencias
- [PRD](./prd.md)
- [Arquitectura General](./architecture.md)
- [Project Brief](./project_brief_mvp_portal_ciudadano.md)

---

> Actualiza este documento conforme evolucione el stack, la arquitectura o las mejores prácticas del equipo.
