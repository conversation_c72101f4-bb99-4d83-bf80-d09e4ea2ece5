"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dependencias/page",{

/***/ "(app-pages-browser)/./src/components/organisms/DependenciasGrid.tsx":
/*!*******************************************************!*\
  !*** ./src/components/organisms/DependenciasGrid.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DependenciasGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_dependenciasApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/services/dependenciasApi */ \"(app-pages-browser)/./src/services/dependenciasApi.ts\");\n/* harmony import */ var _components_atoms_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/atoms/Card */ \"(app-pages-browser)/./src/components/atoms/Card.tsx\");\n/* harmony import */ var _components_atoms_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/atoms/Badge */ \"(app-pages-browser)/./src/components/atoms/Badge.tsx\");\n/* harmony import */ var _components_atoms_Loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/atoms/Loading */ \"(app-pages-browser)/./src/components/atoms/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DependenciasGrid(param) {\n    let { searchQuery = '' } = param;\n    _s();\n    const [dependencias, setDependencias] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DependenciasGrid.useEffect\": ()=>{\n            async function loadDependencias() {\n                try {\n                    setLoading(true);\n                    setError(null);\n                    console.log('Iniciando carga de dependencias...');\n                    const data = await (0,_services_dependenciasApi__WEBPACK_IMPORTED_MODULE_3__.getDependenciasResumen)();\n                    console.log('Dependencias cargadas:', data);\n                    setDependencias(data);\n                } catch (err) {\n                    console.error('Error al cargar dependencias:', err);\n                    setError(err instanceof Error ? err.message : 'Error al cargar dependencias');\n                } finally{\n                    setLoading(false);\n                }\n            }\n            loadDependencias();\n        }\n    }[\"DependenciasGrid.useEffect\"], []);\n    // Filtrar dependencias por búsqueda\n    const filteredDependencias = dependencias.filter((dep)=>{\n        var _dep_descripcion, _dep_sigla;\n        return dep.nombre.toLowerCase().includes(searchQuery.toLowerCase()) || ((_dep_descripcion = dep.descripcion) === null || _dep_descripcion === void 0 ? void 0 : _dep_descripcion.toLowerCase().includes(searchQuery.toLowerCase())) || ((_dep_sigla = dep.sigla) === null || _dep_sigla === void 0 ? void 0 : _dep_sigla.toLowerCase().includes(searchQuery.toLowerCase()));\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_atoms_Loading__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                size: \"lg\",\n                text: \"Cargando dependencias...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-600 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-16 h-16 mx-auto mb-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Error al cargar dependencias\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"btn-primary\",\n                    children: \"Intentar de nuevo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    if (filteredDependencias.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-gray-500 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-16 h-16 mx-auto mb-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"No se encontraron dependencias\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-2\",\n                        children: searchQuery ? 'No hay dependencias que coincidan con \"'.concat(searchQuery, '\"') : 'No hay dependencias disponibles en este momento'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-primary-green mb-2\",\n                        children: \"Dependencias Municipales\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            filteredDependencias.length,\n                            \" dependencia\",\n                            filteredDependencias.length !== 1 ? 's' : '',\n                            searchQuery && \" encontrada\".concat(filteredDependencias.length !== 1 ? 's' : '', ' para \"').concat(searchQuery, '\"')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: filteredDependencias.map((dependencia)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DependenciaCard, {\n                        dependencia: dependencia\n                    }, dependencia.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(DependenciasGrid, \"JPM1UsRwQyC2dnUC+It2xGWWC+s=\");\n_c = DependenciasGrid;\nfunction DependenciaCard(param) {\n    let { dependencia } = param;\n    var _dependencia_subdependencias_, _dependencia_subdependencias, _dependencia_faqs_, _dependencia_faqs;\n    const subdependenciasCount = ((_dependencia_subdependencias = dependencia.subdependencias) === null || _dependencia_subdependencias === void 0 ? void 0 : (_dependencia_subdependencias_ = _dependencia_subdependencias[0]) === null || _dependencia_subdependencias_ === void 0 ? void 0 : _dependencia_subdependencias_.count) || 0;\n    const faqsCount = ((_dependencia_faqs = dependencia.faqs) === null || _dependencia_faqs === void 0 ? void 0 : (_dependencia_faqs_ = _dependencia_faqs[0]) === null || _dependencia_faqs_ === void 0 ? void 0 : _dependencia_faqs_.count) || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_atoms_Card__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        className: \"h-full flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-primary-green mb-1\",\n                                    children: dependencia.nombre\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                dependencia.sigla && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_atoms_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"info\",\n                                    size: \"sm\",\n                                    children: dependencia.sigla\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    dependencia.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-4 line-clamp-3\",\n                        children: dependencia.descripcion\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary-green\",\n                                        children: subdependenciasCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            \"Subdependencia\",\n                                            subdependenciasCount !== 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-lg font-bold text-primary-green\",\n                                        children: faqsCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-600\",\n                                        children: [\n                                            \"FAQ\",\n                                            faqsCount !== 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 mt-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/dependencias/\".concat(dependencia.codigo),\n                        className: \"flex-1 btn-primary text-center text-sm py-2\",\n                        children: \"Ver Detalles\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/tramites?dependencia=\".concat(dependencia.codigo),\n                        className: \"flex-1 btn-secondary text-center text-sm py-2\",\n                        children: \"Ver Tr\\xe1mites\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chia-julio-16-25\\\\src\\\\components\\\\organisms\\\\DependenciasGrid.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DependenciaCard;\nvar _c, _c1;\n$RefreshReg$(_c, \"DependenciasGrid\");\n$RefreshReg$(_c1, \"DependenciaCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/organisms/DependenciasGrid.tsx\n"));

/***/ })

});