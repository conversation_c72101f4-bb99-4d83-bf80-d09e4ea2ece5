-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subdependencias ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tramites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.opas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.faqs ENABLE ROW LEVEL SECURITY;

-- Helper function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS user_role AS $$
BEGIN
    RETURN (SELECT rol FROM public.users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to get user dependencia
CREATE OR REPLACE FUNCTION get_user_dependencia(user_id UUID)
RETURNS UUID AS $$
BEGIN
    RETURN (SELECT dependencia_id FROM public.users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can insert users" ON public.users
    FOR INSERT WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can update all users" ON public.users
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can delete users" ON public.users
    FOR DELETE USING (get_user_role(auth.uid()) = 'admin');

-- Dependencias table policies (public read, admin write)
CREATE POLICY "Anyone can view active dependencias" ON public.dependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Authenticated users can view all dependencias" ON public.dependencias
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can insert dependencias" ON public.dependencias
    FOR INSERT WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can update dependencias" ON public.dependencias
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can delete dependencias" ON public.dependencias
    FOR DELETE USING (get_user_role(auth.uid()) = 'admin');

-- Subdependencias table policies (public read, admin write)
CREATE POLICY "Anyone can view active subdependencias" ON public.subdependencias
    FOR SELECT USING (activo = true);

CREATE POLICY "Authenticated users can view all subdependencias" ON public.subdependencias
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Admins can insert subdependencias" ON public.subdependencias
    FOR INSERT WITH CHECK (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can update subdependencias" ON public.subdependencias
    FOR UPDATE USING (get_user_role(auth.uid()) = 'admin');

CREATE POLICY "Admins can delete subdependencias" ON public.subdependencias
    FOR DELETE USING (get_user_role(auth.uid()) = 'admin');

-- Tramites table policies (public read, funcionario/admin write)
CREATE POLICY "Anyone can view active tramites" ON public.tramites
    FOR SELECT USING (activo = true);

CREATE POLICY "Authenticated users can view all tramites" ON public.tramites
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Funcionarios can manage tramites in their dependencia" ON public.tramites
    FOR ALL USING (
        get_user_role(auth.uid()) = 'funcionario' AND
        subdependencia_id IN (
            SELECT s.id FROM public.subdependencias s
            WHERE s.dependencia_id = get_user_dependencia(auth.uid())
        )
    );

CREATE POLICY "Admins can manage all tramites" ON public.tramites
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- OPAs table policies (public read, funcionario/admin write)
CREATE POLICY "Anyone can view active opas" ON public.opas
    FOR SELECT USING (activo = true);

CREATE POLICY "Authenticated users can view all opas" ON public.opas
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Funcionarios can manage opas in their dependencia" ON public.opas
    FOR ALL USING (
        get_user_role(auth.uid()) = 'funcionario' AND
        subdependencia_id IN (
            SELECT s.id FROM public.subdependencias s
            WHERE s.dependencia_id = get_user_dependencia(auth.uid())
        )
    );

CREATE POLICY "Admins can manage all opas" ON public.opas
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- FAQs table policies (public read, funcionario/admin write)
CREATE POLICY "Anyone can view active faqs" ON public.faqs
    FOR SELECT USING (activo = true);

CREATE POLICY "Authenticated users can view all faqs" ON public.faqs
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Funcionarios can manage faqs in their dependencia" ON public.faqs
    FOR ALL USING (
        get_user_role(auth.uid()) = 'funcionario' AND
        dependencia_id = get_user_dependencia(auth.uid())
    );

CREATE POLICY "Admins can manage all faqs" ON public.faqs
    FOR ALL USING (get_user_role(auth.uid()) = 'admin');

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, nombre)
    VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'nombre');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
