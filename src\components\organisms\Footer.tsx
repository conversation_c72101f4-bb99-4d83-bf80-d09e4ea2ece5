import Link from 'next/link'

const footerLinks = {
  servicios: [
    { name: 'Trámites en Línea', href: '/tramites' },
    { name: 'OPA<PERSON>', href: '/opas' },
    { name: 'Centro de Ayuda', href: '/faqs' },
    { name: '<PERSON><PERSON>', href: '/contacto' },
  ],
  informacion: [
    { name: 'Acerca de Chía', href: '/acerca' },
    { name: 'Transparencia', href: '/transparencia' },
    { name: 'Normatividad', href: '/normatividad' },
    { name: 'PQRS', href: '/pqrs' },
  ],
  legal: [
    { name: 'T<PERSON>rm<PERSON>s de Uso', href: '/terminos' },
    { name: 'Política de Privacidad', href: '/privacidad' },
    { name: 'Política de Cookies', href: '/cookies' },
    { name: 'Accesibilidad', href: '/accesibilidad' },
  ],
}

export default function Footer() {
  return (
    <footer className="bg-primary-green text-white">
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Información de la alcaldía */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-primary-yellow rounded-full flex items-center justify-center">
                <span className="text-black font-bold text-xl">C</span>
              </div>
              <div>
                <h3 className="text-lg font-bold">Alcaldía de Chía</h3>
                <p className="text-sm text-green-100">Portal Ciudadano</p>
              </div>
            </div>
            <p className="text-green-100 text-sm mb-4">
              Transformando la atención ciudadana con tecnología e innovación para una Chía más conectada y eficiente.
            </p>
            <div className="space-y-2 text-sm text-green-100">
              <p>📍 Carrera 11 No. 17-25, Chía, Cundinamarca</p>
              <p>📞 (601) 884-4444</p>
              <p>✉️ <EMAIL></p>
            </div>
          </div>

          {/* Enlaces de servicios */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Servicios</h4>
            <ul className="space-y-2">
              {footerLinks.servicios.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-green-100 hover:text-primary-yellow transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Enlaces de información */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Información</h4>
            <ul className="space-y-2">
              {footerLinks.informacion.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-green-100 hover:text-primary-yellow transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Enlaces legales */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Legal</h4>
            <ul className="space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-green-100 hover:text-primary-yellow transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Redes sociales y horarios */}
        <div className="border-t border-green-600 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex space-x-6">
              <a href="#" className="text-green-100 hover:text-primary-yellow transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
              <a href="#" className="text-green-100 hover:text-primary-yellow transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                </svg>
              </a>
              <a href="#" className="text-green-100 hover:text-primary-yellow transition-colors">
                <span className="sr-only">Instagram</span>
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
                </svg>
              </a>
            </div>
            <div className="text-sm text-green-100">
              <p>Horario de atención: Lunes a Viernes 8:00 AM - 5:00 PM</p>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-green-600 mt-6 pt-6 text-center">
          <p className="text-sm text-green-100">
            © {new Date().getFullYear()} Alcaldía de Chía. Todos los derechos reservados.
          </p>
          <p className="text-xs text-green-200 mt-1">
            Desarrollado con ❤️ para los ciudadanos de Chía
          </p>
        </div>
      </div>
    </footer>
  )
}
