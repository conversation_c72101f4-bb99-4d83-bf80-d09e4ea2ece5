# Portal de Atención Ciudadana de Chía - Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- Transformar la atención ciudadana con IA como pilar central.
- Automatizar y personalizar la experiencia del ciudadano.
- Facilitar autoservicio inteligente y escalamiento eficiente.
- G<PERSON><PERSON><PERSON> accesibilidad, transparencia y mejora continua.
- Integrar un asistente de IA que reduzca el TMO de consultas ciudadanas en un 70% en el primer año.

### Background Context
Este proyecto busca revolucionar la atención ciudadana mediante la integración de inteligencia artificial en todos los puntos de contacto. El enfoque AI-first permitirá una atención más eficiente, personalizada y escalable, alineada con las mejores prácticas internacionales y considerando riesgos éticos, técnicos y de gestión del cambio. El MVP contará con activos de datos estructurados (JSON) para dependencias, subdependencias, trámites, OPAs y FAQs.

### Change Log
| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
| 2025-07-16 | 0.1 | PRD inicial para MVP | John (PM)

## Requirements

### Functional
- FR1: El sistema debe permitir a los ciudadanos consultar y realizar trámites municipales por dependencia y subdependencia.
- FR2: El sistema debe permitir a los funcionarios gestionar y consultar OPAs asociadas a cada subdependencia.
- FR3: El sistema debe integrar un asistente de IA (chatbot) conectado a los datos de Supabase para responder consultas sobre trámites, OPAs y FAQs.
- FR4: El sistema debe ofrecer un centro de ayuda inteligente con FAQs contextuales.
- FR5: El sistema debe generar menús y rutas de navegación dinámicas a partir de la base de datos en Supabase.
- FR6: El sistema debe permitir autoservicio inteligente y seguimiento de solicitudes para ciudadanos.
- FR7: El sistema debe permitir importar los archivos JSON iniciales para poblar la base de datos en Supabase.
- FR8: El sistema debe contar con autenticación de usuarios (funcionarios) para acceder a la gestión de trámites, OPAs y FAQs desde el backend.
- FR9: Los funcionarios autenticados deben poder crear, editar y eliminar trámites, OPAs y FAQs desde una interfaz administrativa.

### Non Functional
- NFR1: La plataforma debe ser accesible (cumplimiento WCAG mínimo AA).
- NFR2: El MVP debe estar desplegado en un entorno seguro y escalable usando Coolify.
- NFR3: El sistema debe responder a consultas en menos de 2 segundos para el 95% de los casos.
- NFR4: El sistema debe ser fácilmente actualizable mediante la edición de los archivos JSON.

## User Interface Design Goals
### Overall UX Vision
- Interfaz intuitiva, moderna y responsiva, accesible desde cualquier dispositivo.
- Experiencia guiada por el asistente de IA para consultas y navegación.

### Key Interaction Paradigms
- Navegación jerárquica por dependencias y subdependencias.
- Búsqueda y filtrado de trámites y OPAs.
- Chatbot visible en todo momento para asistir al usuario.

### Core Screens and Views
- Pantalla de inicio con acceso rápido a servicios y menú de dependencias.
- Vista de trámites por dependencia/subdependencia.
- Vista de OPAs para funcionarios.
- Centro de ayuda/FAQs.
- Interfaz de chat con el asistente de IA.

### Accessibility: WCAG AA
### Branding
- Basado en el diseño base proporcionado.
### Target Device and Platforms
- Web responsive (desktop y móvil).

## Technical Assumptions
- **Frontend:** Next.js (React)
- **Backend y base de datos:** Supabase (los archivos JSON se utilizarán solo para poblar la base de datos inicialmente; toda la gestión y consulta de datos se realizará desde Supabase).
- **Despliegue:** Coolify
- Repositorio: Monorepo
- Arquitectura: Monolítica para el MVP
- Testing: Unitario y pruebas de integración básicas
- El sistema debe contar con autenticación de usuarios (funcionarios) para la gestión administrativa de trámites, OPAs y FAQs.

## Epic List
1. Fundación & Core: Setup, autenticación y gestión básica de usuarios
2. Gestión de Dependencias y Trámites: CRUD y consulta
3. Integración del Asistente de IA y Centro de Ayuda
4. Panel de Funcionarios y Gestión de OPAs
5. Despliegue y Accesibilidad

## Historias de Usuario para Backend/Admin

### Epic: Gestión Administrativa de Trámites, OPAs, FAQs y Usuarios

#### Roles del sistema
- **Ciudadano:** Accede a la consulta de trámites, OPAs y FAQs, y utiliza el autoservicio y el asistente de IA.
- **Funcionario:** Gestiona trámites, OPAs, FAQs, dependencias y subdependencias desde el panel administrativo.
- **Admin:** Gestiona usuarios (crear, editar, eliminar, asignar roles) y accede a un dashboard de estadísticas del sistema (uso, trámites gestionados, actividad de usuarios, etc.).

---

#### Historia 1: Autenticación y roles de usuario
**Como** usuario del sistema (ciudadano, funcionario o admin),  
**quiero** iniciar sesión según mi rol,  
**para** acceder a las funcionalidades correspondientes a mi perfil.

**Criterios de aceptación:**
- El sistema requiere login para acceder a funcionalidades administrativas y de gestión.
- El sistema identifica el rol tras el login y muestra las opciones correspondientes (ciudadano, funcionario, admin).
- Solo los usuarios autenticados y con rol adecuado pueden ver y modificar datos administrativos.

---

#### Historia 2: Gestión de usuarios (solo admin)
**Como** administrador,  
**quiero** crear, editar, eliminar y asignar roles a los usuarios del sistema,  
**para** asegurar la correcta gestión de accesos y permisos.

**Criterios de aceptación:**
- El panel de admin permite ver la lista de usuarios, filtrarlos y buscarlos.
- Se pueden crear nuevos usuarios y asignarles rol (ciudadano, funcionario, admin).
- Es posible editar y eliminar usuarios existentes.
- Solo el admin puede acceder a esta funcionalidad.

---

#### Historia 3: Dashboard de estadísticas (solo admin)
**Como** administrador,  
**quiero** ver un dashboard de estadísticas del sistema,  
**para** monitorear el uso, la actividad de usuarios y la gestión de trámites, OPAs y FAQs.

**Criterios de aceptación:**
- El panel admin muestra estadísticas clave: cantidad de trámites gestionados, actividad de usuarios, consultas realizadas, etc.
- Solo el admin puede acceder al dashboard de estadísticas.

---

#### Historia 2: Importación inicial de datos desde archivos JSON
**Como** administrador,  
**quiero** importar los archivos JSON de trámites, OPAs y FAQs,  
**para** poblar la base de datos en Supabase con la información inicial del sistema.

**Criterios de aceptación:**
- Existe una opción en el panel admin para importar archivos JSON.
- El sistema valida el formato y muestra errores claros si el archivo no es válido.
- Al importar, los datos se almacenan en las tablas correspondientes de Supabase.

---

#### Historia 3: Gestión de trámites
**Como** funcionario autenticado,  
**quiero** crear, editar y eliminar trámites,  
**para** mantener actualizada la información disponible para los ciudadanos.

**Criterios de aceptación:**
- El panel permite ver una lista de trámites, filtrarlos y buscarlos.
- Se pueden crear nuevos trámites mediante un formulario.
- Es posible editar y eliminar trámites existentes.
- Todas las operaciones requieren autenticación.

---

#### Historia 4: Gestión de OPAs
**Como** funcionario autenticado,  
**quiero** crear, editar y eliminar OPAs,  
**para** asegurar que la información de autorizaciones y órdenes esté siempre actualizada.

**Criterios de aceptación:**
- El panel permite ver, crear, editar y eliminar OPAs.
- OPAs están asociadas a subdependencias y trámites.
- Todas las operaciones requieren autenticación.

---

#### Historia 5: Gestión de FAQs
**Como** funcionario autenticado,  
**quiero** crear, editar y eliminar FAQs,  
**para** que el centro de ayuda siempre refleje las preguntas y respuestas más relevantes.

**Criterios de aceptación:**
- El panel permite ver, crear, editar y eliminar FAQs.
- FAQs están asociadas a dependencias, subdependencias y temas.
- Todas las operaciones requieren autenticación.

---

#### Historia 6: Auditoría y trazabilidad
**Como** administrador,  
**quiero** que el sistema registre quién y cuándo realiza cambios en trámites, OPAs y FAQs,  
**para** garantizar trazabilidad y control de cambios.

**Criterios de aceptación:**
- Cada registro tiene campos de fecha de creación, actualización y usuario responsable.
- Es posible consultar el historial de cambios por registro.

---

#### Historia 7: Gestión de dependencias y subdependencias
**Como** funcionario autenticado,  
**quiero** crear, editar y eliminar dependencias y subdependencias,  
**para** mantener la estructura organizacional y la relación con trámites, OPAs y FAQs actualizada.

**Criterios de aceptación:**
- El panel permite ver, crear, editar y eliminar dependencias y subdependencias.
- Las subdependencias están asociadas correctamente a sus dependencias.
- Los cambios en dependencias/subdependencias se reflejan en la asignación de trámites, OPAs y FAQs.
- Todas las operaciones requieren autenticación.

## Next Steps
- Revisar y aprobar este PRD.
- Priorizar historias de usuario y epics.
- Iniciar desarrollo iterativo del MVP.

## Design Architect Prompt
Utilizar este PRD como base para diseñar la arquitectura técnica y los flujos principales del MVP.

## Architect Prompt
Desarrollar la arquitectura y la estructura de carpetas/código usando Next.js, React y Supabase, considerando la integración del asistente de IA y los activos JSON como fuente de datos principal.
