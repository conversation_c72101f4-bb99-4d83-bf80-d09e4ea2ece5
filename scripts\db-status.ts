#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { Database } from '../src/types/database'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function checkDatabaseStatus() {
  console.log('📊 Verificando estado de la base de datos...\n')
  
  try {
    // Verificar dependencias
    const { data: dependencias, error: depError } = await supabase
      .from('dependencias')
      .select('*')
    
    if (depError) {
      console.error('❌ Error consultando dependencias:', depError)
      return
    }
    
    console.log(`📁 Dependencias: ${dependencias?.length || 0} registros`)
    if (dependencias && dependencias.length > 0) {
      console.log('   Ejemplos:')
      dependencias.slice(0, 3).forEach(dep => {
        console.log(`   - ${dep.codigo}: ${dep.nombre} (${dep.sigla})`)
      })
    }
    console.log()
    
    // Verificar subdependencias
    const { data: subdependencias, error: subError } = await supabase
      .from('subdependencias')
      .select('*, dependencias(nombre)')
    
    if (subError) {
      console.error('❌ Error consultando subdependencias:', subError)
      return
    }
    
    console.log(`📂 Subdependencias: ${subdependencias?.length || 0} registros`)
    if (subdependencias && subdependencias.length > 0) {
      console.log('   Ejemplos:')
      subdependencias.slice(0, 3).forEach(sub => {
        console.log(`   - ${sub.codigo}: ${sub.nombre} (${(sub.dependencias as any)?.nombre})`)
      })
    }
    console.log()
    
    // Verificar trámites
    const { data: tramites, error: tramError } = await supabase
      .from('tramites')
      .select('*, subdependencias(nombre, dependencias(nombre))')
    
    if (tramError) {
      console.error('❌ Error consultando trámites:', tramError)
      return
    }
    
    console.log(`📄 Trámites: ${tramites?.length || 0} registros`)
    if (tramites && tramites.length > 0) {
      console.log('   Ejemplos:')
      tramites.slice(0, 3).forEach(tram => {
        const sub = tram.subdependencias as any
        const dep = sub?.dependencias
        console.log(`   - ${tram.codigo_unico}: ${tram.nombre}`)
        console.log(`     └─ ${sub?.nombre} (${dep?.nombre})`)
      })
    }
    console.log()
    
    // Verificar OPAs
    const { data: opas, error: opaError } = await supabase
      .from('opas')
      .select('*, subdependencias(nombre, dependencias(nombre))')
    
    if (opaError) {
      console.error('❌ Error consultando OPAs:', opaError)
      return
    }
    
    console.log(`📋 OPAs: ${opas?.length || 0} registros`)
    if (opas && opas.length > 0) {
      console.log('   Ejemplos:')
      opas.slice(0, 3).forEach(opa => {
        const sub = opa.subdependencias as any
        const dep = sub?.dependencias
        console.log(`   - ${opa.codigo_opa}: ${opa.nombre}`)
        console.log(`     └─ ${sub?.nombre} (${dep?.nombre})`)
      })
    }
    console.log()
    
    // Verificar FAQs
    const { data: faqs, error: faqError } = await supabase
      .from('faqs')
      .select('*, dependencias(nombre)')
    
    if (faqError) {
      console.error('❌ Error consultando FAQs:', faqError)
      return
    }
    
    console.log(`❓ FAQs: ${faqs?.length || 0} registros`)
    if (faqs && faqs.length > 0) {
      // Agrupar por tema
      const temas = faqs.reduce((acc, faq) => {
        const tema = faq.tema || 'Sin tema'
        acc[tema] = (acc[tema] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      console.log('   Por tema:')
      Object.entries(temas).slice(0, 5).forEach(([tema, count]) => {
        console.log(`   - ${tema}: ${count} FAQs`)
      })
      
      console.log('   Ejemplos:')
      faqs.slice(0, 2).forEach(faq => {
        const dep = faq.dependencias as any
        console.log(`   - ${faq.pregunta.substring(0, 60)}...`)
        console.log(`     └─ ${dep?.nombre} (${faq.tema})`)
      })
    }
    console.log()
    
    // Verificar integridad referencial
    console.log('🔍 Verificando integridad referencial...')
    
    // Subdependencias sin dependencia
    const { data: subsSinDep } = await supabase
      .from('subdependencias')
      .select('id, nombre')
      .is('dependencia_id', null)
    
    if (subsSinDep && subsSinDep.length > 0) {
      console.log(`⚠️  ${subsSinDep.length} subdependencias sin dependencia`)
    }
    
    // Trámites sin subdependencia
    const { data: tramSinSub } = await supabase
      .from('tramites')
      .select('id, nombre')
      .is('subdependencia_id', null)
    
    if (tramSinSub && tramSinSub.length > 0) {
      console.log(`⚠️  ${tramSinSub.length} trámites sin subdependencia`)
    }
    
    // OPAs sin subdependencia
    const { data: opasSinSub } = await supabase
      .from('opas')
      .select('id, nombre')
      .is('subdependencia_id', null)
    
    if (opasSinSub && opasSinSub.length > 0) {
      console.log(`⚠️  ${opasSinSub.length} OPAs sin subdependencia`)
    }
    
    // FAQs sin dependencia
    const { data: faqsSinDep } = await supabase
      .from('faqs')
      .select('id, pregunta')
      .is('dependencia_id', null)
    
    if (faqsSinDep && faqsSinDep.length > 0) {
      console.log(`⚠️  ${faqsSinDep.length} FAQs sin dependencia`)
    }
    
    if (!subsSinDep?.length && !tramSinSub?.length && !opasSinSub?.length && !faqsSinDep?.length) {
      console.log('✅ Integridad referencial correcta')
    }
    
    console.log('\n🎉 Verificación completada')
    
  } catch (error) {
    console.error('❌ Error durante la verificación:', error)
    process.exit(1)
  }
}

async function main() {
  await checkDatabaseStatus()
}

if (require.main === module) {
  main()
}
