#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import { Database } from '../src/types/database'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Tipos para los datos JSON
interface TramiteJSON {
  codigo_unico: string
  nombre: string
  formulario: string
  tiempo_respuesta: string
  tiene_pago: string
  visualizacion_suit?: string
  visualizacion_gov?: string
}

interface SubdependenciaJSON {
  nombre: string
  sigla: string
  tramites: TramiteJSON[]
}

interface DependenciaJSON {
  nombre: string
  sigla: string
  subdependencias: Record<string, SubdependenciaJSON>
  correo?: string
  direccion?: string
  extension?: string
  responsable?: string
}

interface TramitesData {
  dependencias: Record<string, DependenciaJSON>
}

interface OpaJSON {
  codigo_OPA: string
  OPA: string
}

interface SubdependenciaOpaJSON {
  nombre: string
  sigla: string
  OPA: OpaJSON[]
}

interface DependenciaOpaJSON {
  nombre: string
  sigla: string
  subdependencias: Record<string, SubdependenciaOpaJSON>
}

interface OpasData {
  dependencias: Record<string, DependenciaOpaJSON>
}

interface FaqJSON {
  pregunta: string
  respuesta: string
  palabras_clave: string[]
}

interface TemaJSON {
  tema: string
  descripcion: string
  preguntas_frecuentes: FaqJSON[]
}

interface FaqDependenciaJSON {
  dependencia: string
  codigo_dependencia: string
  subdependencia: string
  codigo_subdependencia: string
  temas: TemaJSON[]
}

interface FaqsData {
  faqs: FaqDependenciaJSON[]
}

async function clearDatabase() {
  console.log('🧹 Limpiando base de datos...')
  
  // Eliminar en orden inverso por las foreign keys
  await supabase.from('faqs').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('opas').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('tramites').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('subdependencias').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('dependencias').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  
  console.log('✅ Base de datos limpiada')
}

async function seedTramites() {
  console.log('📄 Importando trámites...')
  
  const tramitesPath = join(process.cwd(), 'docs', 'tramites_chia_optimo.json')
  const tramitesData: TramitesData = JSON.parse(readFileSync(tramitesPath, 'utf-8'))
  
  let dependenciasCount = 0
  let subdependenciasCount = 0
  let tramitesCount = 0
  
  for (const [codigoDep, dependencia] of Object.entries(tramitesData.dependencias)) {
    // Insertar dependencia
    const { data: depData, error: depError } = await supabase
      .from('dependencias')
      .insert({
        codigo: codigoDep,
        nombre: dependencia.nombre,
        sigla: dependencia.sigla,
        descripcion: `Correo: ${dependencia.correo || 'N/A'}, Dirección: ${dependencia.direccion || 'N/A'}, Responsable: ${dependencia.responsable || 'N/A'}`
      })
      .select()
      .single()
    
    if (depError) {
      console.error(`❌ Error insertando dependencia ${codigoDep}:`, depError)
      continue
    }
    
    dependenciasCount++
    
    // Insertar subdependencias y trámites
    for (const [codigoSub, subdependencia] of Object.entries(dependencia.subdependencias)) {
      const { data: subData, error: subError } = await supabase
        .from('subdependencias')
        .insert({
          codigo: codigoSub,
          nombre: subdependencia.nombre,
          sigla: subdependencia.sigla,
          dependencia_id: depData.id
        })
        .select()
        .single()
      
      if (subError) {
        console.error(`❌ Error insertando subdependencia ${codigoSub}:`, subError)
        continue
      }
      
      subdependenciasCount++
      
      // Insertar trámites
      for (const tramite of subdependencia.tramites) {
        const { error: tramiteError } = await supabase
          .from('tramites')
          .insert({
            codigo_unico: tramite.codigo_unico,
            nombre: tramite.nombre,
            formulario: tramite.formulario,
            tiempo_respuesta: tramite.tiempo_respuesta,
            tiene_pago: tramite.tiene_pago !== 'No' && tramite.tiene_pago !== '',
            visualizacion_suit: tramite.visualizacion_suit,
            visualizacion_gov: tramite.visualizacion_gov,
            subdependencia_id: subData.id
          })
        
        if (tramiteError) {
          console.error(`❌ Error insertando trámite ${tramite.codigo_unico}:`, tramiteError)
          continue
        }
        
        tramitesCount++
      }
    }
  }
  
  console.log(`✅ Trámites importados: ${dependenciasCount} dependencias, ${subdependenciasCount} subdependencias, ${tramitesCount} trámites`)
}

async function seedOpas() {
  console.log('📋 Importando OPAs...')

  const opasPath = join(process.cwd(), 'docs', 'OPA-chia-optimo.json')
  const opasData: OpasData = JSON.parse(readFileSync(opasPath, 'utf-8'))

  let opasCount = 0

  for (const [codigoDep, dependencia] of Object.entries(opasData.dependencias)) {
    // Buscar la dependencia existente
    const { data: depData } = await supabase
      .from('dependencias')
      .select('id')
      .eq('codigo', codigoDep)
      .single()

    if (!depData) {
      console.warn(`⚠️ Dependencia ${codigoDep} no encontrada para OPAs`)
      continue
    }

    for (const [codigoSub, subdependencia] of Object.entries(dependencia.subdependencias)) {
      // Buscar la subdependencia existente
      const { data: subData } = await supabase
        .from('subdependencias')
        .select('id')
        .eq('codigo', codigoSub)
        .eq('dependencia_id', depData.id)
        .single()

      if (!subData) {
        console.warn(`⚠️ Subdependencia ${codigoSub} no encontrada para OPAs`)
        continue
      }

      // Insertar OPAs
      for (const opa of subdependencia.OPA) {
        const { error: opaError } = await supabase
          .from('opas')
          .insert({
            codigo_opa: opa.codigo_OPA,
            nombre: opa.OPA,
            subdependencia_id: subData.id
          })

        if (opaError) {
          console.error(`❌ Error insertando OPA ${opa.codigo_OPA}:`, opaError)
          continue
        }

        opasCount++
      }
    }
  }

  console.log(`✅ OPAs importadas: ${opasCount} registros`)
}

async function seedFaqs() {
  console.log('❓ Importando FAQs...')

  const faqsPath = join(process.cwd(), 'docs', 'faqs_chia_estructurado.json')
  const faqsData: FaqsData = JSON.parse(readFileSync(faqsPath, 'utf-8'))

  let faqsCount = 0

  for (const faqDep of faqsData.faqs) {
    // Buscar la dependencia existente
    const { data: depData } = await supabase
      .from('dependencias')
      .select('id')
      .eq('codigo', faqDep.codigo_dependencia)
      .single()

    if (!depData) {
      console.warn(`⚠️ Dependencia ${faqDep.codigo_dependencia} no encontrada para FAQs`)
      continue
    }

    for (const tema of faqDep.temas) {
      for (const faq of tema.preguntas_frecuentes) {
        const { error: faqError } = await supabase
          .from('faqs')
          .insert({
            pregunta: faq.pregunta,
            respuesta: faq.respuesta,
            palabras_clave: faq.palabras_clave,
            dependencia_id: depData.id,
            tema: tema.tema
          })

        if (faqError) {
          console.error(`❌ Error insertando FAQ:`, faqError)
          continue
        }

        faqsCount++
      }
    }
  }

  console.log(`✅ FAQs importadas: ${faqsCount} registros`)
}

async function main() {
  console.log('🚀 Iniciando migración de datos...')

  try {
    await clearDatabase()
    await seedTramites()
    await seedOpas()
    await seedFaqs()

    console.log('🎉 Migración completada exitosamente!')
  } catch (error) {
    console.error('❌ Error durante la migración:', error)
    process.exit(1)
  }
}

// Ejecutar solo si es llamado directamente
if (require.main === module) {
  main()
}
