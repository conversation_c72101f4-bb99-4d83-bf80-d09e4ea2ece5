import Button from '@/components/atoms/Button'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'

export default function HomePage() {
  return (
    <div className="bg-gray-50">
      <div className="container-custom py-12">
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-primary-green mb-4">
            Portal de Atención Ciudadana
          </h1>
          <h2 className="text-2xl text-primary-yellow font-semibold mb-2">
            Alcaldía de Chía
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Accede a todos los trámites y servicios municipales de forma rápida y sencilla
          </p>
          <Badge variant="info" className="mt-4">
            🤖 Asistente IA disponible 24/7
          </Badge>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <Card>
            <h3 className="text-xl font-semibold text-primary-green mb-3">Trámites</h3>
            <p className="text-gray-600 mb-4">
              Consulta y realiza trámites municipales
            </p>
            <Button variant="primary">
              Ver Trámites
            </Button>
          </Card>

          <Card>
            <h3 className="text-xl font-semibold text-primary-green mb-3">OPAs</h3>
            <p className="text-gray-600 mb-4">
              Otras Prestaciones de Atención al ciudadano
            </p>
            <Button variant="primary">
              Ver OPAs
            </Button>
          </Card>

          <Card>
            <h3 className="text-xl font-semibold text-primary-green mb-3">Centro de Ayuda</h3>
            <p className="text-gray-600 mb-4">
              Preguntas frecuentes y asistencia
            </p>
            <Button variant="primary">
              Ver FAQs
            </Button>
          </Card>
        </div>

        <div className="text-center">
          <p className="text-gray-500">
            ¿Necesitas ayuda? Nuestro asistente virtual está disponible 24/7
          </p>
        </div>
      </div>
    </div>
  )
}
