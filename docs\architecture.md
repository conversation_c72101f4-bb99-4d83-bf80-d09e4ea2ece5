# Arquitectura General del Sistema – Portal Ciudadano

## 1. Visión General

El sistema es una plataforma web modular, segura y escalable, compuesta por:
- **Frontend:** Next.js (React) – SPA/SSR con rutas públicas y protegidas.
- **Backend & BDD:** Supabase (PostgreSQL, Auth, Storage, Realtime).
- **Despliegue:** Coolify (CI/CD, monitoreo, gestión de entornos).
- **Infraestructura:** Cloud-agnostic, orientada a contenedores y escalabilidad horizontal.

---

## 2. Componentes Principales

### a) Frontend (Next.js)
- Rutas públicas: Consulta de trámites, OPAs, FAQs, autoservicio, chatbot IA.
- Rutas privadas: Panel administrativo (funcionario/admin), dashboard de estadísticas (solo admin).
- Gestión de autenticación: Uso de Supabase Auth (JWT), roles y protección de rutas.
- Consumo de APIs: Directamente hacia Supabase (REST o GraphQL).

### b) Backend (Supabase)
- Base de datos relacional (PostgreSQL): Tablas para usuarios, roles, trámites, OPAs, dependencias, subdependencias, FAQs, logs de auditoría.
- Auth: Gestión de usuarios, roles (ciudadano, funcionario, admin), recuperación y registro seguro.
- Policies y RLS: Row Level Security para proteger datos según rol.
- Funciones (RPC): Lógica avanzada (importación de JSON, estadísticas, auditoría).

### c) Integraciones y servicios
- Chatbot IA: Integrado en frontend, accediendo a datos en Supabase y APIs externas si es necesario.
- Notificaciones: Emails/SMS (opcional, vía Supabase o servicios externos).
- Importación de datos: Panel admin permite cargar archivos JSON para poblar la BDD.

### d) Despliegue e Infraestructura (Coolify)
- CI/CD: Despliegue automatizado de frontend y backend.
- Entornos: Producción y pruebas, con rollback sencillo.
- Monitoreo: Logs, métricas básicas, alertas configurables.

---

## 3. Mejores Prácticas Aplicadas

- Seguridad: Autenticación y autorización estricta, RLS en Supabase, validación de datos, HTTPS obligatorio.
- Escalabilidad: Arquitectura desacoplada, escalable horizontalmente, uso de CDN para assets.
- Accesibilidad: Cumplimiento WCAG AA en UI.
- Auditoría y trazabilidad: Logs de cambios, historial de usuarios y operaciones críticas.
- Desarrollo ágil: Monorepo, CI/CD, integración y despliegue continuo.
- Developer Experience: Documentación clara, scripts de setup, entorno de pruebas local.
- Costos y sostenibilidad: Uso de tecnologías open source, monitoreo de consumo y escalado bajo demanda.

---

## 4. Diagrama de Alto Nivel (Descriptivo)

```
[Usuario] 
   │
   ▼
[Next.js Frontend] ──► [Supabase Auth & API] ──► [PostgreSQL DB]
   │                          │
   │                          └─► [Storage (archivos, adjuntos)]
   │
   └─► [Chatbot IA (integrado, consulta datos y APIs)]
   │
[Coolify] (gestiona despliegue, CI/CD y monitoreo de ambos)
```

---

## 5. Estructura de Carpetas Recomendada

```
/project-root
│
├── /apps
│   ├── /web                # Next.js frontend y rutas API
│   └── /admin-scripts      # Scripts CLI para importación, migraciones, etc.
├── /supabase
│   ├── /migrations         # SQL de migraciones y seeds
│   ├── /functions          # Edge Functions
│   └── /policies           # Políticas RLS y seguridad
├── /docs                   # Documentación, PRD, diagramas, etc.
├── /scripts                # Automatización (deploy, backup, etc.)
├── .env*
├── package.json
├── README.md
└── ...otros archivos
```

---

## 6. Flujo de Autenticación Multi-Rol

1. Registro/Login: Supabase Auth (email/password, OAuth). Rol por defecto: ciudadano; admin puede cambiarlo a funcionario o admin.
2. Obtención del rol: Tras login, el frontend consulta el campo `rol` en la tabla `users`. AuthContext mantiene el estado de autenticación y el rol.
3. Protección de rutas: públicas (consulta y autoservicio), privadas (panel admin/funcionario, dashboard solo admin).
4. RLS en Supabase: Policies para que solo admin gestione usuarios, funcionarios gestionen trámites/OPAs/FAQs, y ciudadanos solo consulten.

---

## 7. Modelo de Datos Resumido

- **users:** id (uuid), email, nombre, rol (ciudadano/funcionario/admin), fecha_creacion, activo
- **dependencias/subdependencias:** Estructura jerárquica para organización municipal
- **tramites, opas, faqs:** Asociados a subdependencias/dependencias, con trazabilidad de autoría y fecha
- **logs_auditoria:** Registro de acciones críticas para trazabilidad y cumplimiento

(Relaciones y diagrama detallado en el archivo correspondiente de modelo de datos)

---

## 8. Recomendaciones de Seguridad

- Activar y auditar Row Level Security (RLS) en todas las tablas sensibles
- Validar y sanitizar datos tanto en frontend como backend
- Forzar HTTPS en todos los entornos
- Uso de variables de entorno seguras (.env) y rotación periódica de claves
- Limitar permisos de Storage y funciones RPC según rol
- Auditoría activa de accesos y cambios críticos

---

## 9. Monitoreo y Observabilidad

- Activar logs de aplicación y base de datos
- Integrar alertas de errores críticos y caídas (por ejemplo, Sentry, Grafana, Prometheus)
- Monitoreo de consumo y performance en Supabase y Coolify
- Dashboards de métricas clave accesibles solo para admin

---

## 10. CI/CD y Entornos

- Automatizar despliegues con Coolify (build, test, deploy)
- Múltiples entornos: desarrollo, pruebas y producción
- Rollback sencillo y versionado de migraciones
- Backups automáticos y pruebas de restauración periódicas

---

## 11. Developer Experience (DX)

- Monorepo con scripts de setup y migración
- Documentación clara y actualizada en /docs
- Entorno local reproducible (docker-compose opcional)
- Linter, formateo y tests automatizados en pipeline
- Uso de convenciones de ramas y PRs para control de calidad

---


