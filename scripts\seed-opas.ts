#!/usr/bin/env tsx

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { join } from 'path'
import { Database } from '../src/types/database'

// Configuración de Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Error: Variables de entorno NEXT_PUBLIC_SUPABASE_URL y SUPABASE_SERVICE_ROLE_KEY son requeridas')
  process.exit(1)
}

const supabase = createClient<Database>(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

interface OpaJSON {
  codigo_OPA: string
  OPA: string
}

interface SubdependenciaOpaJSON {
  nombre: string
  sigla: string
  OPA: OpaJSON[]
}

interface DependenciaOpaJSON {
  nombre: string
  sigla: string
  subdependencias: Record<string, SubdependenciaOpaJSON>
}

interface OpasData {
  dependencias: Record<string, DependenciaOpaJSON>
}

async function seedOpas() {
  console.log('📋 Importando solo OPAs...')
  
  // Limpiar OPAs existentes
  await supabase.from('opas').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  
  const opasPath = join(process.cwd(), 'docs', 'OPA-chia-optimo.json')
  const opasData: OpasData = JSON.parse(readFileSync(opasPath, 'utf-8'))
  
  let opasCount = 0
  let dependenciasEncontradas = 0
  let subdependenciasEncontradas = 0
  
  for (const [codigoDep, dependencia] of Object.entries(opasData.dependencias)) {
    // Buscar la dependencia existente
    const { data: depData } = await supabase
      .from('dependencias')
      .select('id, nombre')
      .eq('codigo', codigoDep)
      .single()
    
    if (!depData) {
      console.warn(`⚠️ Dependencia ${codigoDep} (${dependencia.nombre}) no encontrada. Asegúrate de ejecutar seed-tramites.ts primero.`)
      continue
    }
    
    dependenciasEncontradas++
    console.log(`✅ Procesando dependencia: ${depData.nombre}`)
    
    for (const [codigoSub, subdependencia] of Object.entries(dependencia.subdependencias)) {
      // Buscar la subdependencia existente
      const { data: subData } = await supabase
        .from('subdependencias')
        .select('id, nombre')
        .eq('codigo', codigoSub)
        .eq('dependencia_id', depData.id)
        .single()
      
      if (!subData) {
        console.warn(`⚠️ Subdependencia ${codigoSub} (${subdependencia.nombre}) no encontrada en dependencia ${depData.nombre}`)
        continue
      }
      
      subdependenciasEncontradas++
      
      // Insertar OPAs
      for (const opa of subdependencia.OPA) {
        const { error: opaError } = await supabase
          .from('opas')
          .insert({
            codigo_opa: opa.codigo_OPA,
            nombre: opa.OPA,
            subdependencia_id: subData.id
          })
        
        if (opaError) {
          console.error(`❌ Error insertando OPA ${opa.codigo_OPA}:`, opaError)
          continue
        }
        
        opasCount++
      }
      
      console.log(`   ✅ ${subdependencia.OPA.length} OPAs insertadas en ${subData.nombre}`)
    }
  }
  
  console.log(`🎉 OPAs importadas exitosamente:`)
  console.log(`   - ${dependenciasEncontradas} dependencias procesadas`)
  console.log(`   - ${subdependenciasEncontradas} subdependencias procesadas`)
  console.log(`   - ${opasCount} OPAs insertadas`)
}

async function main() {
  try {
    await seedOpas()
  } catch (error) {
    console.error('❌ Error durante la importación:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
