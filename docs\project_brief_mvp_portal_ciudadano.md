# Project Brief: MVP Portal de Atención Ciudadana (Alcaldía de Chía)

## 1. Objetivo Principal
Transformar la atención ciudadana con IA como pilar central:
- Automatizar y personalizar la experiencia del ciudadano.
- Facilitar autoservicio inteligente y escalamiento eficiente.
- Garantizar accesibilidad, transparencia y mejora continua.

## 2. Usuarios Principales
- Funcionarios de la alcaldía
- Ciudadanos del municipio

## 3. Problema / Necesidad
Revolucionar la atención ciudadana mediante la integración de inteligencia artificial en todos los puntos de contacto. El enfoque AI-first permitirá una atención más eficiente, personalizada y escalable, alineada con las mejores prácticas internacionales.

## 4. Funcionalidades Mínimas del MVP
- Gestión de trámites municipales
- Gestión y consulta de OPAs (Órdenes de Pago y Autorizaciones de Servicios)
- Acceso organizado por dependencias y subdependencias de la alcaldía
- Autoservicio inteligente para ciudadanos (consultas, solicitudes, seguimiento)
- Panel para funcionarios para gestión ágil y eficiente
- Integración de un asistente de IA (chatbot) para facilitar la consulta de información por parte de los ciudadanos, guiándolos en la búsqueda de trámites, OPAs y respuestas a preguntas frecuentes. El asistente debe estar conectado a los archivos estructurados (JSON) para responder consultas sobre dependencias, trámites y FAQs de manera automática y contextual. El objetivo es reducir el tiempo medio de gestión (TMO) de las consultas ciudadanas en al menos un 70% durante el primer año.

## 5. Activos de Datos Disponibles
El MVP contará con los siguientes recursos estructurados:
- **OPA-chia-optimo.json:** Jerarquía de dependencias y subdependencias, con OPAs asociadas.
  - Ejemplo:  
    - Dependencia: “Secretaría de Medio Ambiente”  
      - Subdependencia: “Forestal”  
        - OPAs:  
          - “Concepto para aprovechamiento forestal en espacio público”  
          - “Poda de árboles en espacio público y privado”
- **tramites_chia_optimo.json:** Trámites detallados por subdependencia.
  - Ejemplo:  
    - Dependencia: “Secretaría de Medio Ambiente”  
      - Subdependencia: “Publicidad”  
        - Trámite: “Registro de la publicidad exterior visual”  
        - Formulario, tiempo de respuesta, tarifa, enlaces oficiales
- **faqs_chia_estructurado.json:** FAQs por dependencia/subdependencia y tema.
  - Ejemplo:  
    - Dependencia: “Despacho del alcalde”  
      - Subdependencia: “TIC”  
        - Tema: “Internet gratuito y acceso a equipos de cómputo”  
        - Pregunta: “¿En qué consiste?”  
        - Respuesta: “Se cuenta con 26 equipos disponibles para el público...”

## 6. Ejemplos de funcionalidades y flujos concretos
- Menú jerárquico dinámico de dependencias y subdependencias.
- Búsqueda y gestión de trámites con información detallada y enlaces oficiales.
- Gestión/consulta de OPAs por subdependencia para funcionarios.
- Centro de ayuda inteligente con FAQs contextuales.
- Autoservicio escalable mediante actualización de archivos JSON.
- Interacción del ciudadano con el asistente de IA para:
  - Consultar qué trámite realizar y cómo hacerlo.
  - Obtener información sobre OPAs y dependencias.
  - Recibir respuestas automáticas a preguntas frecuentes.
  - Ser guiado paso a paso en procesos administrativos.

## 7. Requerimientos Técnicos, Legales o de Accesibilidad
- No hay requerimientos específicos adicionales indicados en esta fase.

## 8. Plazo Ideal para el MVP
- 15 días

## 9. Referencias / Diseño Base
- Se adjunta diseño base proporcionado por el usuario.

## 10. Criterios de Éxito
- Los funcionarios pueden gestionar de manera rápida los trámites y OPAs de la alcaldía.
- Consulta efectiva y autoservicio para la ciudadanía.
- Experiencia de usuario accesible, transparente y en mejora continua.

## 11. Arquitectura Tecnológica del MVP
- **Frontend:** Next.js (React)
- **Backend y base de datos:** Supabase
- **Despliegue:** Coolify

**Ventajas de esta arquitectura:**
- Next.js permite SSR/SSG y una experiencia de usuario moderna y veloz.
- Supabase ofrece autenticación, base de datos y almacenamiento en tiempo real, ideal para prototipos rápidos y escalabilidad.
- Coolify simplifica el despliegue y la gestión de la infraestructura, permitiendo despliegues automáticos y monitoreo sencillo.
