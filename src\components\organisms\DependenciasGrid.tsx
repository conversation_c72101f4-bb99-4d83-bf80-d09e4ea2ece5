'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getDependenciasResumen } from '@/services/dependenciasApi'
import Card from '@/components/atoms/Card'
import Badge from '@/components/atoms/Badge'
import Loading from '@/components/atoms/Loading'
import type { Dependencia } from '@/types'

interface DependenciaConStats extends Dependencia {
  subdependencias: { count: number }[]
  faqs: { count: number }[]
}

interface DependenciasGridProps {
  searchQuery?: string
}

export default function DependenciasGrid({ searchQuery = '' }: DependenciasGridProps) {
  const [dependencias, setDependencias] = useState<DependenciaConStats[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function loadDependencias() {
      try {
        setLoading(true)
        setError(null)
        const data = await getDependenciasResumen()
        setDependencias(data as DependenciaConStats[])
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error al cargar dependencias')
      } finally {
        setLoading(false)
      }
    }

    loadDependencias()
  }, [])

  // Filtrar dependencias por búsqueda
  const filteredDependencias = dependencias.filter(dep =>
    dep.nombre.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dep.descripcion?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dep.sigla?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loading size="lg" text="Cargando dependencias..." />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold">Error al cargar dependencias</h3>
          <p className="text-gray-600 mt-2">{error}</p>
        </div>
        <button 
          onClick={() => window.location.reload()}
          className="btn-primary"
        >
          Intentar de nuevo
        </button>
      </div>
    )
  }

  if (filteredDependencias.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-semibold">No se encontraron dependencias</h3>
          <p className="text-gray-600 mt-2">
            {searchQuery 
              ? `No hay dependencias que coincidan con "${searchQuery}"`
              : 'No hay dependencias disponibles en este momento'
            }
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header con estadísticas */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-primary-green mb-2">
          Dependencias Municipales
        </h2>
        <p className="text-gray-600">
          {filteredDependencias.length} dependencia{filteredDependencias.length !== 1 ? 's' : ''} 
          {searchQuery && ` encontrada${filteredDependencias.length !== 1 ? 's' : ''} para "${searchQuery}"`}
        </p>
      </div>

      {/* Grid de dependencias */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDependencias.map((dependencia) => (
          <DependenciaCard 
            key={dependencia.id} 
            dependencia={dependencia} 
          />
        ))}
      </div>
    </div>
  )
}

interface DependenciaCardProps {
  dependencia: DependenciaConStats
}

function DependenciaCard({ dependencia }: DependenciaCardProps) {
  const subdependenciasCount = dependencia.subdependencias?.[0]?.count || 0
  const faqsCount = dependencia.faqs?.[0]?.count || 0

  return (
    <Card className="h-full flex flex-col">
      <div className="flex-1">
        {/* Header de la tarjeta */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-primary-green mb-1">
              {dependencia.nombre}
            </h3>
            {dependencia.sigla && (
              <Badge variant="info" size="sm">
                {dependencia.sigla}
              </Badge>
            )}
          </div>
        </div>

        {/* Descripción */}
        {dependencia.descripcion && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {dependencia.descripcion}
          </p>
        )}

        {/* Estadísticas */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="text-lg font-bold text-primary-green">
              {subdependenciasCount}
            </div>
            <div className="text-xs text-gray-600">
              Subdependencia{subdependenciasCount !== 1 ? 's' : ''}
            </div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="text-lg font-bold text-primary-green">
              {faqsCount}
            </div>
            <div className="text-xs text-gray-600">
              FAQ{faqsCount !== 1 ? 's' : ''}
            </div>
          </div>
        </div>
      </div>

      {/* Acciones */}
      <div className="flex gap-2 mt-auto">
        <Link 
          href={`/dependencias/${dependencia.codigo}`}
          className="flex-1 btn-primary text-center text-sm py-2"
        >
          Ver Detalles
        </Link>
        <Link 
          href={`/tramites?dependencia=${dependencia.codigo}`}
          className="flex-1 btn-secondary text-center text-sm py-2"
        >
          Ver Trámites
        </Link>
      </div>
    </Card>
  )
}
