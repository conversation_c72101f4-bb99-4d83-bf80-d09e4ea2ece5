-- Function to search across tramites, opas, and faqs
CREATE OR REPLACE FUNCTION search_content(
    search_query TEXT,
    limit_results INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    tipo TEXT,
    titulo TEXT,
    descripcion TEXT,
    dependencia TEXT,
    subdependencia TEXT,
    url TEXT,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    (
        -- Search in tramites
        SELECT 
            t.id,
            'tramite'::TEXT as tipo,
            t.nombre as titulo,
            COALESCE(t.formulario, '') as descripcion,
            d.nombre as dependencia,
            s.nombre as subdependencia,
            '/tramites/' || t.codigo_unico as url,
            ts_rank(to_tsvector('spanish', t.nombre), plainto_tsquery('spanish', search_query)) as rank
        FROM public.tramites t
        JOIN public.subdependencias s ON t.subdependencia_id = s.id
        JOIN public.dependencias d ON s.dependencia_id = d.id
        WHERE t.activo = true 
        AND to_tsvector('spanish', t.nombre) @@ plainto_tsquery('spanish', search_query)
        
        UNION ALL
        
        -- Search in opas
        SELECT 
            o.id,
            'opa'::TEXT as tipo,
            o.nombre as titulo,
            '' as descripcion,
            d.nombre as dependencia,
            s.nombre as subdependencia,
            '/opas/' || o.codigo_opa as url,
            ts_rank(to_tsvector('spanish', o.nombre), plainto_tsquery('spanish', search_query)) as rank
        FROM public.opas o
        JOIN public.subdependencias s ON o.subdependencia_id = s.id
        JOIN public.dependencias d ON s.dependencia_id = d.id
        WHERE o.activo = true 
        AND to_tsvector('spanish', o.nombre) @@ plainto_tsquery('spanish', search_query)
        
        UNION ALL
        
        -- Search in faqs
        SELECT 
            f.id,
            'faq'::TEXT as tipo,
            f.pregunta as titulo,
            f.respuesta as descripcion,
            d.nombre as dependencia,
            NULL::TEXT as subdependencia,
            '/faqs#' || f.id::TEXT as url,
            ts_rank(
                to_tsvector('spanish', f.pregunta || ' ' || f.respuesta), 
                plainto_tsquery('spanish', search_query)
            ) as rank
        FROM public.faqs f
        JOIN public.dependencias d ON f.dependencia_id = d.id
        WHERE f.activo = true 
        AND (
            to_tsvector('spanish', f.pregunta || ' ' || f.respuesta) @@ plainto_tsquery('spanish', search_query)
            OR f.palabras_clave && string_to_array(lower(search_query), ' ')
        )
    )
    ORDER BY rank DESC
    LIMIT limit_results;
END;
$$ LANGUAGE plpgsql;

-- Function to get popular tramites (for dashboard)
CREATE OR REPLACE FUNCTION get_popular_tramites(limit_results INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    nombre TEXT,
    codigo_unico TEXT,
    dependencia TEXT,
    subdependencia TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.nombre,
        t.codigo_unico,
        d.nombre as dependencia,
        s.nombre as subdependencia
    FROM public.tramites t
    JOIN public.subdependencias s ON t.subdependencia_id = s.id
    JOIN public.dependencias d ON s.dependencia_id = d.id
    WHERE t.activo = true
    ORDER BY t.created_at DESC
    LIMIT limit_results;
END;
$$ LANGUAGE plpgsql;

-- Function to get dashboard statistics
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_tramites', (SELECT COUNT(*) FROM public.tramites WHERE activo = true),
        'total_opas', (SELECT COUNT(*) FROM public.opas WHERE activo = true),
        'total_faqs', (SELECT COUNT(*) FROM public.faqs WHERE activo = true),
        'total_dependencias', (SELECT COUNT(*) FROM public.dependencias WHERE activo = true),
        'total_subdependencias', (SELECT COUNT(*) FROM public.subdependencias WHERE activo = true)
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to get tramites by dependencia
CREATE OR REPLACE FUNCTION get_tramites_by_dependencia(dependencia_codigo TEXT)
RETURNS TABLE (
    id UUID,
    codigo_unico TEXT,
    nombre TEXT,
    formulario TEXT,
    tiempo_respuesta TEXT,
    tiene_pago BOOLEAN,
    subdependencia TEXT,
    dependencia TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.codigo_unico,
        t.nombre,
        t.formulario,
        t.tiempo_respuesta,
        t.tiene_pago,
        s.nombre as subdependencia,
        d.nombre as dependencia
    FROM public.tramites t
    JOIN public.subdependencias s ON t.subdependencia_id = s.id
    JOIN public.dependencias d ON s.dependencia_id = d.id
    WHERE t.activo = true 
    AND d.codigo = dependencia_codigo
    ORDER BY t.nombre;
END;
$$ LANGUAGE plpgsql;

-- Function to get faqs by tema
CREATE OR REPLACE FUNCTION get_faqs_by_tema(tema_filter TEXT DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    pregunta TEXT,
    respuesta TEXT,
    tema TEXT,
    palabras_clave TEXT[],
    dependencia TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        f.id,
        f.pregunta,
        f.respuesta,
        f.tema,
        f.palabras_clave,
        d.nombre as dependencia
    FROM public.faqs f
    JOIN public.dependencias d ON f.dependencia_id = d.id
    WHERE f.activo = true 
    AND (tema_filter IS NULL OR f.tema = tema_filter)
    ORDER BY f.tema, f.pregunta;
END;
$$ LANGUAGE plpgsql;

-- Function to validate user permissions
CREATE OR REPLACE FUNCTION user_can_manage_content(
    user_id UUID,
    content_dependencia_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    user_role user_role;
    user_dep_id UUID;
BEGIN
    SELECT rol, dependencia_id INTO user_role, user_dep_id
    FROM public.users WHERE id = user_id;
    
    -- Admins can manage everything
    IF user_role = 'admin' THEN
        RETURN TRUE;
    END IF;
    
    -- Funcionarios can only manage content in their dependencia
    IF user_role = 'funcionario' AND user_dep_id = content_dependencia_id THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
