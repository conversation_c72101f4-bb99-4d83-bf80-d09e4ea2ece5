# Scripts de Migración de Datos - Portal Ciudadano Chía

Este directorio contiene los scripts necesarios para migrar los datos desde los archivos JSON a la base de datos Supabase.

## Archivos Disponibles

### Scripts de Migración

- **`seed-database.ts`** - Script principal que ejecuta toda la migración completa
- **`seed-tramites.ts`** - Importa solo trámites (incluye dependencias y subdependencias)
- **`seed-opas.ts`** - Importa solo OPAs (requiere que existan dependencias y subdependencias)
- **`seed-faqs.ts`** - Importa solo FAQs (requiere que existan dependencias)

### Scripts de Utilidad

- **`db-status.ts`** - Verifica el estado actual de la base de datos y muestra estadísticas

## Requisitos Previos

1. **Variables de Entorno**: Asegúrate de tener configuradas las siguientes variables en tu archivo `.env.local`:
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=tu_url_de_supabase
   SUPABASE_SERVICE_ROLE_KEY=tu_service_role_key
   ```

2. **Archivos JSON**: Los siguientes archivos deben estar en el directorio `docs/`:
   - `tramites_chia_optimo.json`
   - `OPA-chia-optimo.json`
   - `faqs_chia_estructurado.json`

3. **Base de Datos**: La base de datos debe tener las migraciones aplicadas (tablas creadas).

## Uso

### Migración Completa

Para ejecutar la migración completa de todos los datos:

```bash
npm run db:seed
# o directamente:
tsx scripts/seed-database.ts
```

### Migración Individual

Para ejecutar migraciones específicas:

```bash
# Solo trámites (incluye dependencias y subdependencias)
npm run db:seed:tramites
# o: tsx scripts/seed-tramites.ts

# Solo OPAs (requiere que existan dependencias/subdependencias)
npm run db:seed:opas
# o: tsx scripts/seed-opas.ts

# Solo FAQs (requiere que existan dependencias)
npm run db:seed:faqs
# o: tsx scripts/seed-faqs.ts
```

### Verificación del Estado

Para verificar el estado actual de la base de datos:

```bash
npm run db:status
# o: tsx scripts/db-status.ts
```

## Orden de Ejecución

Si ejecutas las migraciones individualmente, sigue este orden:

1. **Primero**: `seed-tramites.ts` (crea dependencias, subdependencias y trámites)
2. **Segundo**: `seed-opas.ts` (requiere dependencias y subdependencias existentes)
3. **Tercero**: `seed-faqs.ts` (requiere dependencias existentes)

## Estructura de Datos

### Dependencias
- Código único
- Nombre y sigla
- Información de contacto (correo, dirección, responsable)

### Subdependencias
- Código único
- Nombre y sigla
- Relación con dependencia padre

### Trámites
- Código único
- Nombre y descripción
- Información de formulario y tiempo de respuesta
- Enlaces a visualización en SUIT y GOV.CO
- Relación con subdependencia

### OPAs (Otras Prestaciones de Atención)
- Código único
- Nombre/descripción
- Relación con subdependencia

### FAQs (Preguntas Frecuentes)
- Pregunta y respuesta
- Palabras clave para búsqueda
- Tema de categorización
- Relación con dependencia

## Características de los Scripts

### Limpieza de Datos
- Los scripts limpian los datos existentes antes de insertar nuevos
- Se respetan las restricciones de foreign keys

### Manejo de Errores
- Cada script maneja errores individualmente
- Continúa procesando aunque falle un registro específico
- Proporciona logs detallados de errores

### Validación de Integridad
- El script `db-status.ts` verifica la integridad referencial
- Detecta registros huérfanos o inconsistencias

### Logging Detallado
- Cada script proporciona información detallada del progreso
- Muestra estadísticas de registros procesados
- Indica errores específicos con contexto

## Solución de Problemas

### Error: Variables de entorno no encontradas
Asegúrate de tener el archivo `.env.local` con las variables correctas.

### Error: Archivos JSON no encontrados
Verifica que los archivos JSON estén en el directorio `docs/`.

### Error: Tablas no existen
Ejecuta las migraciones de Supabase primero:
```bash
supabase db reset
```

### Error: Dependencias no encontradas (OPAs/FAQs)
Ejecuta primero `seed-tramites.ts` para crear las dependencias y subdependencias.

## Comandos NPM Disponibles

```bash
npm run db:setup      # Migración completa + seed
npm run db:seed       # Solo seed completo
npm run db:seed:tramites  # Solo trámites
npm run db:seed:opas      # Solo OPAs
npm run db:seed:faqs      # Solo FAQs
npm run db:status         # Verificar estado
npm run db:reset          # Reset completo + seed
```
